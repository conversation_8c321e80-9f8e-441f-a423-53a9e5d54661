import { defineStore } from 'pinia';
import { message } from 'ant-design-vue';
// import { parseSRT } from '../lib/utils';

export const useSubtitleStore = defineStore('subtitle', {
  state: () => ({
    subtitles: [],
    isLoading: false,
    error: null,
    currentPage: 1,
    pageSize: 10,
    totalPages: 0,
    editingId: null,
    editText: '',
    retryingBatch: null,
    expandedTable: false,
    selectedVoice: 'isVoice1',
    renderVoiceOptions: {
      // Text Subtitle options
      isVoice1: {
        showSubtitle: true,
        subtitleFontSize: 48,
        subtitleTextColor: '#ffffff',
        subtitleBackgroundColor: '#000000',
        subtitleBorderColor: '#000000',
        subtitleBold: true,
        shadowSize: 2,
        currentPlayingSubtitleId: null,
        activeSubtitleId: null,
        assOptions: {
          posX: 0,
          posY: 33,
          rotation: 0,
          align: 5,
          fontSize: 48,
        },
      },
      isVoice2: {
        showSubtitle: true,
        subtitleFontSize: 48,
        subtitleTextColor: '#000000',
        subtitleBackgroundColor: '#FFCC00',
        subtitleBorderColor: '#000000',
        subtitleBold: true,
        shadowSize: 2,
        currentPlayingSubtitleId: null,
        activeSubtitleId: null,
        assOptions: {
          posX: 0,
          posY: 33,
          rotation: 0,
          align: 5,
          fontSize: 48,
        },
      },
      isVoice3: {
        showSubtitle: true,
        subtitleFontSize: 48,
        subtitleTextColor: '#ffffff',
        subtitleBackgroundColor: '#0066CC',
        subtitleBorderColor: '#000000',
        subtitleBold: true,
        shadowSize: 2,
        currentPlayingSubtitleId: null,
        activeSubtitleId: null,
        assOptions: {
          posX: 0,
          posY: 33,
          rotation: 0,
          align: 5,
          fontSize: 48,
        },
      },


    },

    renderOptions: {
      // Text options
      showText: true,
      fontSize: 24,
      textColor: '#fff700',
      textValue: '@Hello World',
      textOpacity: 50,
      textDirection: 'random',

      // Text Subtitle options
      showSubtitle: true,
      subtitleFontSize: 48,
      subtitleTextColor: '#ffffff',
      subtitleBackgroundColor: '#000000',
      subtitleBorderColor: '#000000',
      subtitleBold: true,
      shadowSize: 2,
      currentPlayingSubtitleId: null,
      activeSubtitleId: null,
      assOptions: {
        posX: 0,
        posY: 33,
        rotation: 0,
        align: 5,
        fontSize: 48,
      },

      // Logo options
      showLogo: false,
      logoPosition: 'bottom-right',
      logoSize: 'medium',

      // Audio options
      addBackgroundMusic: false,
      backgroundMusicVolume: 30,
      originalAudioVolume: 80,
      holdOriginalAudio: false,
      holdMusicOnly: false,

      // Output options
      videoQuality: '1080p/16:9',
      frameRate: '30',
    },
  }),
  persist: {
    storage: localStorage,
    pick: ['renderOptions','renderVoiceOptions'],
  },
  actions: {
    setSubtitles(subtitles) {
      this.subtitles = subtitles;
      this.totalPages = Math.ceil(subtitles.length / this.pageSize);
    },
    setIsLoading(value) {
      this.isLoading = value;
    },
    setError(error) {
      this.error = error;
    },
    setCurrentPage(page) {
      this.currentPage = page;
    },
    setEditingId(id) {
      this.editingId = id;
    },
    setEditText(text) {
      this.editText = text;
    },
    setRetryingBatch(batch) {
      this.retryingBatch = batch;
    },
    setExpandedTable(value) {
      this.expandedTable = value;
    },
  },
});
