<script setup>
import { ref, onMounted } from 'vue';
import { message, theme } from 'ant-design-vue';
import { useTTSStore } from './stores/ttsStore';
import CookieInput from './components/CookieInput.vue';
import TextToSpeech from './components/TextToSpeech.vue';
import SrtProcessor from './components/SrtProcessor.vue';
import SrtTableProcessor from './components/SrtTableProcessor.vue';
import AudioList from './components/AudioList.vue';
import VideoSpeedAdjuster from './components/VideoSpeedAdjuster.vue';
import WhisperProcessor from './components/WhisperProcessor.vue';
import VideoOcrProcessor from './components/VideoOcrProcessor.vue';
import VideoCutter from './components/VideoCutter.vue';
import SrtVideoRenderer from './components/SrtVideoRenderer.vue';
import SRTVideoRenderer2 from './components/SRTVideoRenderer2.vue';
import { createI18nProvider } from './i18n/i18n';
import ConsoleLog from './components/ConsoleLog.vue';
import SubtitleTranslatorTerm from './components/SubtitleTranslatorTerm.vue';
import VideoProcessor from './components/VideoProcessor.vue';
import Progress from './components/Progress.vue';


createI18nProvider();
const ttsStore = useTTSStore();
const themeConfig = {
    token: {
        // Background Colors
        colorBgContainer: '#121212',
        colorBgLayout: '#121212',
        colorBgElevated: '#1B1B1C',
        colorBgSpotlight: '#070709',
        colorFillContent: '#29292D',
        colorFillContentHover: '#323232',
        colorFillAlter: '#1F1F1F',
        colorBgContainerDisabled: '#1F1F1F',
        colorBgTextHover: '#323232',
        colorBgTextActive: '#3E3E3E',

        // Text Colors
        colorText: '#C2CAC6',
        colorTextTertiary: '#C2CAC6',
        colorTextPlaceholder: '#666666',
        colorTextDisabled: '#4D4D4D',
        colorTextHeading: '#E6E6E6',
        colorTextLabel: '#BFBFBF',
        colorTextDescription: '#8C8C8C',
        colorTextLightSolid: '#FFFFFF',

        // Border and Split Colors
        colorBorderBg: '#1F1F1F',
        colorSplit: 'rgba(255, 255, 255, 0.12)',

        // Primary Colors
        colorPrimary: '#00C1CD',
        colorLink: '#00C1CD',
        colorLinkHover: '#33CED8',
        colorLinkActive: '#008C96',

        // Custom Menu Colors
        colorMenu: '#1B1B1C',
        darkItemSelectedBg: '#32304b',
        colorDisplayContainer: '#29292D',

        // Icon Colors
        colorIcon: '#8C8C8C',
        colorIconHover: '#BFBFBF',

        // Outline Colors
        controlOutline: 'rgba(0, 193, 205, 0.2)',
        colorWarningOutline: 'rgba(255, 215, 5, 0.2)',
        colorErrorOutline: 'rgba(255, 77, 79, 0.2)',
        controlTmpOutline: 'rgba(0, 193, 205, 0.2)',

        // Motion
        motionDurationSlow: '0.6s',
        motionDurationMid: '0.6s',
    },
    components: {
        Button: {
            primaryColor: '#181819',
            colorPrimary: '#00C1CD',
        },
        Menu: {
            darkItemBg: '#1B1B1C',
            darkItemHoverBg: '#32304b',
            darkItemSelectedBg: '#32304b',
        },
        Card: {
            colorBgContainer: '#1B1B1C',
        },
        Modal: {
            contentBg: '#1B1B1C',
        },
        Table: {
            colorBgContainer: '#1B1B1C',
            headerBg: '#29292D',
        },
    },
    algorithm: [theme.darkAlgorithm, theme.compactAlgorithm],
};
onMounted(() => {
  ttsStore.fetchSpeakers();
  electronAPI.on('on-error', (event, error) => {
    console.error(error);
    message.error(error);
  });
});
</script>

<template>
  <a-config-provider :theme="themeConfig">
    <div class="absolute inset-0 w-full h-full bg-gray-900 flex flex-col overflow-hidden">
      <!-- Fixed Header -->
      <header class="flex-shrink-0 bg-gray-800 shadow-lg border-b border-gray-700">
        <div class="flex items-center h-16 px-4">
          <h1 class="text-2xl font-bold text-white">TTS App</h1>
          <Progress class="w-1/3 mx-4" />
          <div class="flex-1"></div>
          <ConsoleLog />
        </div>
      </header>

      <!-- Main Content Area - Fixed Height -->
      <main class="flex-1 min-h-0 flex flex-col">
        <!-- Tabs Container -->
        <div class="flex-1 flex flex-col min-h-0">
          <a-tabs 
            v-model:activeKey="ttsStore.activeTab" 
            class="flex-1 flex flex-col min-h-0"
            :tab-bar-style="{ flexShrink: 0, paddingLeft: '16px', paddingRight: '16px' }"
          >
            <a-tab-pane key="tts" tab="Text to Speech" class="flex-1 min-h-0">
              <div class="h-full overflow-auto p-4">
                <TextToSpeech />
              </div>
            </a-tab-pane>

            <a-tab-pane key="srt" tab="Subtitle Translator" class="flex-1 min-h-0">
              <!-- <div class="h-full overflow-auto p-4"> -->
                <PanelPage />
              <!-- </div> -->
            </a-tab-pane>

            <a-tab-pane key="srt-table" tab="SRT Table" class="flex-1 min-h-0">
              <div class="h-full overflow-auto p-4">
                <SrtTableProcessor />
              </div>
            </a-tab-pane>

            <a-tab-pane key="history" tab="Generated Audio" class="">
            <div class="flex-1  min-h-0">
              <AudioList />
            </div>
            </a-tab-pane>

            <a-tab-pane key="video-speed" tab="Video Speed" class="flex-1 min-h-0">
              <div class="h-full overflow-auto p-4">
                <VideoSpeedAdjuster />
              </div>
            </a-tab-pane>

            <a-tab-pane key="whisper" tab="Whisper Transcription" class="flex-1 min-h-0">
              <div class="h-full overflow-auto p-4">
                <WhisperProcessor />
              </div>
            </a-tab-pane>

            <a-tab-pane key="video-ocr" tab="Video OCR" class="flex-1 min-h-0">
              <div class="h-full overflow-auto p-4">
                <VideoOcrProcessor />
              </div>
            </a-tab-pane>

            <a-tab-pane key="video-cutter" tab="Video Cutter" class="flex-1 min-h-0">
              <div class="h-full overflow-auto p-4">
                <VideoCutter />
              </div>
            </a-tab-pane>

            <a-tab-pane key="video-renderer" tab="SRT Video Renderer" class="flex-1 min-h-0">
              <div class="h-full overflow-auto p-4">
                <SrtVideoRenderer />
              </div>
            </a-tab-pane>

            <a-tab-pane key="config" tab="Config" class="flex-1 min-h-0">
              <div class="h-full overflow-auto p-4">
                <CookieInput />
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </main>
    </div>
  </a-config-provider>
</template>

<style>
/* Global styles để đảm bảo không có scroll toàn cục */
html, body {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* Custom styles cho Ant Design tabs để fit với layout cố định */
.ant-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ant-tabs-content-holder {
  flex: 1;
  min-height: 0;
}

.ant-tabs-tabpane {
  height: 100%;
}

/* Đảm bảo tab bar không chiếm quá nhiều không gian */
.ant-tabs-tab-bar {
  flex-shrink: 0;
  margin-bottom: 0;
}

/* Custom scrollbar cho các khu vực có thể scroll */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #1F1F1F;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #666666;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #888888;
}
</style>