<script setup>
import { ref, onMounted } from 'vue';
import { message, theme } from 'ant-design-vue';
import { useTTSStore } from './stores/ttsStore';
import CookieInput from './components/CookieInput.vue';
import TextToSpeech from './components/TextToSpeech.vue';
import SrtProcessor from './components/SrtProcessor.vue';
import SrtTableProcessor from './components/SrtTableProcessor.vue';
import AudioList from './components/AudioList.vue';
import VideoSpeedAdjuster from './components/VideoSpeedAdjuster.vue';
import WhisperProcessor from './components/WhisperProcessor.vue';
import VideoOcrProcessor from './components/VideoOcrProcessor.vue';
import VideoCutter from './components/VideoCutter.vue';
import SrtVideoRenderer from './components/SrtVideoRenderer.vue';
import SRTVideoRenderer2 from './components/SRTVideoRenderer2.vue';
import { createI18nProvider } from './i18n/i18n';
import ConsoleLog from './components/ConsoleLog.vue';
import SubtitleTranslatorTerm from './components/SubtitleTranslatorTerm.vue';
import VideoProcessor from './components/VideoProcessor.vue';
import Progress from './components/Progress.vue';


createI18nProvider();
const ttsStore = useTTSStore();
const themeConfig = {
    token: {
        // Background Colors
        colorBgContainer: '#121212',
        colorBgLayout: '#121212',
        colorBgElevated: '#1B1B1C',
        colorBgSpotlight: '#070709',
        colorFillContent: '#29292D',
        colorFillContentHover: '#323232',
        colorFillAlter: '#1F1F1F',
        colorBgContainerDisabled: '#1F1F1F',
        colorBgTextHover: '#323232',
        colorBgTextActive: '#3E3E3E',

        // Text Colors
        colorText: '#C2CAC6',
        colorTextTertiary: '#C2CAC6',
        colorTextPlaceholder: '#666666',
        colorTextDisabled: '#4D4D4D',
        colorTextHeading: '#E6E6E6',
        colorTextLabel: '#BFBFBF',
        colorTextDescription: '#8C8C8C',
        colorTextLightSolid: '#FFFFFF',

        // Border and Split Colors
        colorBorderBg: '#1F1F1F',
        colorSplit: 'rgba(255, 255, 255, 0.12)',

        // Primary Colors
        colorPrimary: '#00C1CD',
        colorLink: '#00C1CD',
        colorLinkHover: '#33CED8',
        colorLinkActive: '#008C96',

        // Custom Menu Colors
        colorMenu: '#1B1B1C',
        darkItemSelectedBg: '#32304b',
        colorDisplayContainer: '#29292D',

        // Icon Colors
        colorIcon: '#8C8C8C',
        colorIconHover: '#BFBFBF',

        // Outline Colors
        controlOutline: 'rgba(0, 193, 205, 0.2)',
        colorWarningOutline: 'rgba(255, 215, 5, 0.2)',
        colorErrorOutline: 'rgba(255, 77, 79, 0.2)',
        controlTmpOutline: 'rgba(0, 193, 205, 0.2)',

        // Motion
        motionDurationSlow: '0.6s',
        motionDurationMid: '0.6s',
    },
    components: {
        Button: {
            primaryColor: '#181819',
            colorPrimary: '#00C1CD',
        },
        Menu: {
            darkItemBg: '#1B1B1C',
            darkItemHoverBg: '#32304b',
            darkItemSelectedBg: '#32304b',
        },
        Card: {
            colorBgContainer: '#1B1B1C',
        },
        Modal: {
            contentBg: '#1B1B1C',
        },
        Table: {
            colorBgContainer: '#1B1B1C',
            headerBg: '#29292D',
        },
    },
    algorithm: [theme.darkAlgorithm, theme.compactAlgorithm],
};
onMounted(() => {
  ttsStore.fetchSpeakers();
  electronAPI.on('on-error', (event, error) => {
    console.error(error);
    message.error(error);
  });
});
</script>

<template>
  <a-config-provider :theme="themeConfig">
  <div class="min-h-screen w-full bg-gray-100 dark:bg-gray-900 ">
    <header class="bg-white dark:bg-gray-800 shadow">
      <div class="flex max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">TTS App</h1>
        <Progress  class="w-1/3" />
        <div class="flex-1"></div>
        <ConsoleLog />
      </div>
    </header>

    <main class="max-w-7xl mx-auto py-6 px-2 sm:px-6 lg:px-8">
      <!-- Cookie Input -->
      <!-- <CookieInput /> -->

      <!-- Tabs -->
      <a-tabs v-model:activeKey="ttsStore.activeTab" class="mt-6">
        <a-tab-pane key="tts" tab="Text to Speech">
          <TextToSpeech />
        </a-tab-pane>

        <a-tab-pane key="srt" tab="Subtitle Translator">
          <SubtitleTranslatorTerm />
        </a-tab-pane>

        <a-tab-pane key="srt-table" tab="SRT Table">
          <SrtTableProcessor />
        </a-tab-pane>

        <a-tab-pane key="history" tab="Generated Audio">
          <AudioList />
        </a-tab-pane>
        <a-tab-pane key="video-speed" tab="Video Speed">
          <VideoSpeedAdjuster />
        </a-tab-pane>
        <a-tab-pane key="whisper" tab="Whisper Transcription">
          <WhisperProcessor />
        </a-tab-pane>
        <a-tab-pane key="video-ocr" tab="Video OCR">
          <VideoOcrProcessor />
        </a-tab-pane>
        <a-tab-pane key="video-cutter" tab="Video Cutter">
          <VideoCutter />
        </a-tab-pane>
        <a-tab-pane key="video-renderer" tab="SRT Video Renderer">
          <SrtVideoRenderer />
        </a-tab-pane>
        <!-- <a-tab-pane key="srt-video-renderer-2" tab="SRT Video Renderer 2">
          <SRTVideoRenderer2 />
        </a-tab-pane> -->
        <!-- <a-tab-pane key="video-renderer-2" tab="SRT Video Renderer 2">
          <VideoProcessor />
        </a-tab-pane> -->
        <a-tab-pane key="config" tab="Config">
          <CookieInput />
        </a-tab-pane>
      </a-tabs>
    </main>

    <!-- <footer class="bg-white dark:bg-gray-800 shadow mt-auto py-4">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-gray-500 dark:text-gray-400">
        <p>CapCut TTS App - Built with Vue, Electron, and Tailwind CSS</p>
      </div>
    </footer> -->
  </div>
  </a-config-provider>
</template>

<style>
/* Global styles */
</style>
