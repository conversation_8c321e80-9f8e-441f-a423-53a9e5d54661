<template>
  <!-- Container với layout pattern chuẩn -->
  <div class="flex-1 flex flex-col bg-gray-900 text-white min-h-0">
    <!-- Batch error quick retry section -->
    <div v-if="onRetryBatch && errorBatchCount > 0" class="mx-4 mb-2 p-3 border border-amber-200 bg-amber-50 rounded">
      <div class="flex items-start gap-2">
        <AlertTwoTone twoToneColor="#faad14" class="mt-1 flex-shrink-0" />
        <div class="flex-1">
          <h3 class="text-sm font-medium text-amber-800">
            Quick batch retry
          </h3>
          <p class="text-xs text-amber-700 mb-2">
            {{ t("batchErrorDisplay.description") }}
          </p>

          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 mt-1">
            <div v-for="batch in errorBatchesWithErrors" :key="`batch-${batch.actualBatchIndex}`"
              class="flex items-center justify-between py-1 px-2 bg-white border border-amber-100 rounded text-sm">
              <div class="truncate">
                <span class="font-medium">
                  {{ t("subtitleTable.batch") }} {{ batch.actualBatchIndex + 1 }}:
                </span>
                #{{ batch.firstId }}-{{ batch.lastId }}
                <span class="ml-1 text-rose-600 text-xs">
                  ({{ batch.errorCount }} {{ t("subtitleTable.errors") }})
                </span>
              </div>
              <a-button size="small" type="text" @click="handleRetryBatch(batch.actualBatchIndex)"
                :disabled="retryingBatch === batch.actualBatchIndex || translating"
                class="h-6 px-2 text-xs text-amber-600 hover:text-amber-700 hover:bg-amber-100">
                <template v-if="retryingBatch === batch.actualBatchIndex">
                  <LoadingOutlined class="mr-1" />
                  {{ t("common.retrying") }}
                </template>
                <template v-else>
                  <ReloadOutlined class="mr-1" />
                  {{ t("common.retry") }}
                </template>
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Subtitle table -->
    <div class="overflow-x-auto -mx-4 sm:mx-0">
      <div ref="tableContainerRef"
        :class="`${expandedTable ? 'max-h-[800px]' : 'max-h-[400px]'
          } bg-slate-800 custom-scrollbar overflow-y-auto border border-gray-700 rounded-md transition-all duration-300 scroll-container`">
        <a-table :dataSource="currentPageSubtitles" :columns="columns" :pagination="false" size="small"
          :custom-row="customRow" :scroll="{ x: 1000 }" :rowClassName="getRowClassName">
          <!-- ID Column -->
          <template #bodyCell="{ column, record }">
            <div :class="[record.isEnabled ? '' : 'opacity-50']">
              <div v-if="column.key === 'id'"
                :class="`flex items-center ${record.id === state.currentPlayingSubtitleId ? 'bg-green-800' : ''}`">
                {{ record.id }}
                <span v-if="record.id % batchSize === 1" class="ml-1 text-xs text-gray-400">
                  B{{ Math.floor((record.id - 1) / batchSize) + 1 }}
                </span>
                <!-- checkbox for isEnabled -->
                <a-checkbox class="ml-1" :checked="record.isEnabled" @click.stop
                  @change.stop="() => toggleEnabledForSubtitle(record.id)"
                  :title="record.isEnabled ? 'Disable audio' : 'Enable audio'" :style="{ transform: 'scale(1.2)' }" />
              </div>

              <!-- Time Column -->
              <template v-else-if="column.key === 'time'">
                <div class="text-xs text-gray-500 whitespace-nowrap">
                  <div>{{ record.start }}</div>
                  <div class="text-gray-400">↓</div>
                  <div>{{ record.end }}</div>
                </div>
              </template>

              <!-- Original Text Column -->
              <template v-else-if="column.key === 'translatedText'">
                <!-- 2 item col -->
                <div class="flex flex-row gap-2" v-if="!(editingId1 === record.id)">
                  <div
                    class="max-w-xs whitespace-pre-wrap break-words text-sm max-h-[120px] custom-scrollbar text-gray-200"
                    @click.stop="handleEdit1(record.id, record.text)">
                    {{ record.text }}
                  </div>
                  <div class="flex-1"></div>
                  <!-- screen shot and ocr text handler -->
                  <div class="flex">
                    <a-button size="small" @click.stop="handleReText(record)" :loading="isLoading[record.id]">
                     <scan-outlined />
                    </a-button>
                  </div>
                </div>
                <div v-if="editingId1 === record.id" class="space-y-2">
                  <a-textarea v-model:value="editText1" :rows="2"
                    class="w-full min-h-[80px] max-h-[150px] text-sm custom-scrollbar" />
                  <div class="flex gap-2">
                    <a-button size="small" type="primary" @click.stop="handleSave1(record.id)">
                      {{ t("common.save") }}
                    </a-button>
                    <a-button size="small" @click.stop="handleCancel1">
                      {{ t("common.cancel") }}
                    </a-button>
                  </div>
                </div>

                <!-- </template> -->

                <!-- Translation Column -->
                <!-- <template v-else-if="column.key === 'translatedText'"> -->
                <div class="relative">
                  <!-- Editing mode -->
                  <div v-if="editingId === record.id" class="space-y-2">
                    <a-textarea v-model:value="editText" :rows="2"
                      class="w-full min-h-[80px] max-h-[150px] text-sm custom-scrollbar" />
                    <div class="flex gap-2">
                      <a-button size="small" type="primary" @click.stop="handleSave(record.id)">
                        {{ t("common.save") }}
                      </a-button>
                      <a-button size="small" @click.stop="handleCancel">
                        {{ t("common.cancel") }}
                      </a-button>
                    </div>
                  </div>

                  <!-- Display mode -->
                  <div v-else :class="`max-w-xs whitespace-pre-wrap break-words max-h-[120px] custom-scrollbar relative ${record.status === 'error' ? 'text-rose-600' : 'text-gray-100'
                    }`">
                    <div v-if="record.status === 'error'">
                      <div class="text-sm text-rose-600">
                        {{ record.error || t("errors.translationError") }}
                      </div>
                      <a-button type="link" class="text-xs mt-1 text-rose-600 hover:text-rose-700 p-0"
                        @click.stop="onRetry(record.id)" :disabled="translating">
                        {{ t("common.retry") }}
                      </a-button>
                    </div>
                    <div v-else-if="record.translatedText" @click.stop="handleEdit(record.id, record.translatedText)">
                      <!-- <highlighted-text v-if="activeGlossary" :text="record.translatedText" :glossary="activeGlossary"
                        class="text-gray-800" />
                      <template v-else>
                        {{ record.translatedText }}
                      </template> -->
                      {{ record.translatedText }}
                    </div>
                    <span v-else-if="record.status === 'pending'" class="text-gray-400 italic text-sm">
                      {{ t("subtitleTable.waitingToTranslate") }}
                    </span>
                    <span v-else-if="record.status === 'translating'"
                      class="px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-600 flex items-center">
                      <LoadingOutlined class="mr-1" />
                      {{ t("subtitleTable.translating") }}
                    </span>
                    <span v-else class="text-blue-400 italic text-sm">
                      {{ t("subtitleTable.translating") }}
                    </span>
                    <!-- Suggestions panel -->
                    <div v-if="record.id === suggestingId" class="suggestion-panel absolute top-full left-0 w-full z-10"
                      @click.stop>
                      <div class="px-3 py-2 bg-blue-50 border-b border-blue-100">
                        <div class="flex justify-between items-center">
                          <h4 class="text-sm font-medium text-blue-800 flex items-center">
                            <ThunderboltOutlined class="mr-1.5 text-blue-500" />
                            {{ t("subtitleTable.aiSuggestions") }}
                          </h4>
                          <a-button type="text" size="small" class="h-6 w-6 p-0 rounded-full" @click="closeSuggestions">
                            ×
                          </a-button>
                        </div>
                        <p class="text-xs text-blue-600 mt-1">
                          {{ t("subtitleTable.chooseSuggestion") }}
                        </p>
                      </div>

                      <!-- Loading state -->
                      <div v-if="loadingSuggestions" class="py-8 text-center">
                        <LoadingOutlined class="text-xl mb-2 text-blue-500" />
                        <p class="text-sm text-gray-500">
                          {{ t("common.loading") }}
                        </p>
                      </div>

                      <!-- Suggestions list -->
                      <div v-else-if="suggestions.length > 0">
                        <div v-for="(suggestion, idx) in suggestions" :key="`suggestion-${idx}`"
                          class="suggestion-option hover:bg-gray-50" @click="applyTranslationSuggestion(suggestion)">
                          <div :class="`suggestion-label label-${getLabelType(idx)}`">
                            {{ getLabelText(idx) }}
                          </div>
                          <p class="text-sm text-gray-800 whitespace-pre-wrap">
                            {{ suggestion }}
                          </p>
                          <div class="flex justify-between items-center mt-2">
                            <p class="text-xs text-blue-500">
                              {{ t("subtitleTable.clickToApply") }}
                            </p>
                            <a-button size="small" type="text" class="h-6 text-xs py-0 px-2"
                              @click.stop="applyTranslationSuggestion(suggestion)">
                              Áp dụng
                            </a-button>
                          </div>
                        </div>
                      </div>

                      <!-- No suggestions -->
                      <div v-else class="py-4 text-center">
                        <p class="text-sm text-gray-500">
                          {{ t("subtitleTable.noSuggestions") }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </template>

              <!-- Action Column -->
              <template v-else-if="column.key === 'action'">
                <div class="flex flex-col space-y-1" @click.stop>
                  <div class="flex items-center space-x-1 gap-2.5 justify-center">
                    <SoundOutlined />
                    <a-checkbox :checked="isVoiceSelectedForSubtitle(record.id, 1)"
                      @change="() => toggleVoiceForSubtitle(record.id, 1)" :style="{ transform: 'scale(1.2)' }" />
                    <Audio v-if="record.isGenerated1" :src="record.audioUrl1" />
                    <a-checkbox :checked="isVoiceSelectedForSubtitle(record.id, 2)"
                      @change="() => toggleVoiceForSubtitle(record.id, 2)" :style="{ transform: 'scale(1.2)' }" />
                    <Audio v-if="record.isGenerated2" :src="record.audioUrl2" />
                    <a-checkbox :checked="isVoiceSelectedForSubtitle(record.id, 3)"
                      @change="() => toggleVoiceForSubtitle(record.id, 3)" :style="{ transform: 'scale(1.2)' }" />
                    <Audio v-if="record.isGenerated3" :src="record.audioUrl3" />
                    <a-button size="small" @click="generateAudioForSubtitle(record, true)" :disabled="generatingAudio"
                      :loading="generatingAudio && state.currentPlayingSubtitleId === record.id" title="Generate Audio">
                      <reload-outlined v-if="!(generatingAudio && state.currentPlayingSubtitleId === record.id)" />
                    </a-button>
                  </div>
                </div>
                <SubtitleActions :record="record" :editing-id="editingId" :translating="translating"
                  :loading-suggestions="loadingSuggestions" :retrying-batch="retryingBatch" :batch-size="batchSize"
                  :on-suggest-translation="onSuggestTranslation" @play="handlePlayVideo" @edit="handleEdit"
                  @suggest="handleSuggestTranslation" @retry="onRetry" @delete="handleDelete"
                  @insert-before="handleInsertBefore" @insert-after="handleInsertAfter" @split="handleSplit"
                  @merge="handleMerge" />
              </template>
            </div>
          </template>
        </a-table>
      </div>

      <!-- Table footer area -->
      <div class="flex justify-between items-center mt-3 px-4">
        <div class="text-xs text-gray-500">
          {{ t("subtitleTable.showing") }}
          <span class="font-medium">{{ subtitles.length }}</span>
          {{ t("subtitleTable.subtitles") }}
        </div>

        <a-button type="text" size="small" @click="setExpandedTable(!expandedTable)"
          class="text-gray-500 hover:text-gray-700 px-2 py-1 h-8">
          <template v-if="expandedTable">
            <UpOutlined class="h-4 w-4 mr-1" />
            {{ t("subtitleTable.collapseTable") }}
          </template>
          <template v-else>
            <DownOutlined class="h-4 w-4 mr-1" />
            {{ t("subtitleTable.expandTable") }}
          </template>
        </a-button>

        <!-- Pagination -->
        <div class="flex gap-1 items-center justify-center">
          <a-button type="default" size="small" @click="goToPage(1)" :disabled="currentPage <= 1">
            <template #icon>
              <DoubleLeftOutlined />
            </template>
          </a-button>

          <a-button type="default" size="small" @click="goToPage(currentPage - 1)" :disabled="currentPage <= 1">
            <template #icon>
              <LeftOutlined />
            </template>
          </a-button>

          <span class="flex items-center px-3 text-sm">
            Trang {{ currentPage }}/{{ totalPages }}
          </span>

          <a-button type="default" size="small" @click="goToPage(currentPage + 1)"
            :disabled="currentPage >= totalPages">
            <template #icon>
              <RightOutlined />
            </template>
          </a-button>

          <a-button type="default" size="small" @click="goToPage(totalPages)" :disabled="currentPage >= totalPages">
            <template #icon>
              <DoubleRightOutlined />
            </template>
          </a-button>
        </div>

        <div class="text-xs text-gray-500 text-right">
          <span class="font-medium">
            {{subtitles.filter(s => s.status === "translated").length}}
          </span>
          {{ t("subtitleTable.translated") }},
          <span class="font-medium ml-1">
            {{subtitles.filter(s => s.status === "error").length}}
          </span>
          {{ t("subtitleTable.errors") }}
        </div>
      </div>
    </div>

    <!-- Insert Modal -->
    <SubtitleInsertModal v-model:open="showInsertModal" :target-id="insertTargetId"
      :target-subtitle="insertTargetSubtitle" :position="insertPosition" @confirm="handleInsertConfirm" />

    <!-- Split Modal -->
    <SubtitleSplitModal v-model:open="showSplitModal" :subtitle="splitTargetSubtitle" @confirm="handleSplitConfirm" />
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch, onMounted, nextTick } from 'vue';
import {
  UpOutlined,
  DownOutlined,
  EditOutlined,
  ReloadOutlined,
  ThunderboltOutlined,
  LoadingOutlined,
  AlertTwoTone,
  LeftOutlined,
  RightOutlined,
  DoubleLeftOutlined,
  DoubleRightOutlined,
  SoundOutlined,
  PlayCircleOutlined,
  PauseOutlined,
  DeleteOutlined,
  ScanOutlined
} from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
// import HighlightedText from './HighlightedText.vue';
import { useI18n } from '@/i18n/i18n';
import { useSubtitleStore } from '@/stores/subtitle-store';
import { useTTSStore } from '@/stores/ttsStore';
import Audio from './Audio.vue';
import SubtitleActions from './SubtitleActions.vue';
import SubtitleInsertModal from './SubtitleInsertModal.vue';
import SubtitleSplitModal from './SubtitleSplitModal.vue';
import { state } from '@/lib/state';
import { parseTimeToSeconds } from '@/lib/utils';
import {
  reorderSubtitleIds,
  adjustSubtitleTimes,
  splitSubtitle,
  mergeSubtitles,
  insertSubtitle
} from '@/lib/subtitleUtils';

export default defineComponent({
  name: 'SubtitleTable',
  components: {
    UpOutlined,
    DownOutlined,
    EditOutlined,
    ReloadOutlined,
    ThunderboltOutlined,
    LoadingOutlined,
    AlertTwoTone,
    LeftOutlined,
    RightOutlined,
    DoubleLeftOutlined,
    DoubleRightOutlined,
    SoundOutlined,
    Audio,
    PlayCircleOutlined,
    PauseOutlined,
    DeleteOutlined,
    SubtitleActions,
    SubtitleInsertModal,
    SubtitleSplitModal,
    SoundOutlined,
    ScanOutlined
  },
  props: {
    subtitles: {
      type: Array,
      required: true
    },
    onRetry: {
      type: Function,
      required: true
    },
    onRetryBatch: {
      type: Function,
      default: null
    },
    onUpdateTranslation: {
      type: Function,
      required: true
    },
    onUpdateOriginalText: {
      type: Function,
      required: true
    },
    translating: {
      type: Boolean,
      default: false
    },
    batchSize: {
      type: Number,
      default: 10
    },
    highlightedSubtitleId: {
      type: Number,
      default: null
    },
    onSuggestTranslation: {
      type: Function,
      default: null
    },
    activeGlossary: {
      type: Object,
      default: null
    },
    isVoiceSelectedForSubtitle: {
      type: Function,
      default: () => false
    },
    toggleVoiceForSubtitle: {
      type: Function,
      default: () => { }
    },
    generateAudioForSubtitle: {
      type: Function,
      default: () => { }
    },
    generatingAudio: {
      type: Boolean,
      default: false
    },
    currentGeneratingId: {
      type: Number,
      default: null
    },
    onPlayVideo: {
      type: Function,
      default: null
    },
    onDelete: {
      type: Function,
      default: null
    },
    onInsert: {
      type: Function,
      default: null
    },
    onSplit: {
      type: Function,
      default: null
    },
    onMerge: {
      type: Function,
      default: null
    },
    onReorder: {
      type: Function,
      default: null
    }
  },
  setup(props) {
    const { t } = useI18n();
    const subtitleStore = useSubtitleStore();
    const ttsStore = useTTSStore();

    // State variables
    const editingId = ref(null);
    const editingId1 = ref(null);
    const editText = ref('');
    const editText1 = ref('');
    const retryingBatch = ref(null);
    const expandedTable = ref(false);
    const tableContainerRef = ref(null);
    const highlightedRowRef = ref(null);
    const suggestingId = ref(null);
    const suggestions = ref([]);
    const loadingSuggestions = ref(false);
    const isLoading = ref({});

    // Modal states
    const showInsertModal = ref(false);
    const showSplitModal = ref(false);
    const insertTargetId = ref(null);
    const insertTargetSubtitle = ref(null);
    const insertPosition = ref('after');
    const splitTargetSubtitle = ref(null);

    // Table pagination
    const currentPage = computed(() => subtitleStore.currentPage);
    const rowsPerPage = 30;

    // Calculate total pages
    const totalPages = computed(() =>
      Math.ceil(props.subtitles.length / rowsPerPage)
    );

    // Get current page subtitles
    const currentPageSubtitles = computed(() => {
      if (props.subtitles.length === 0) return [];

      const start = (currentPage.value - 1) * rowsPerPage;
      const end = Math.min(start + rowsPerPage, props.subtitles.length);
      return props.subtitles.slice(start, end);
    });

    // Table columns
    const columns = [
      {
        title: t("subtitleTable.id"),
        key: 'id',
        width: 60,
        className: 'bg-slate-800 text-white'
      },
      {
        title: t("subtitleTable.time"),
        key: 'time',
        width: 120,
        className: 'bg-slate-800 text-white'
      },
      // {
      //   title: t("subtitleTable.originalText"),
      //   key: 'text',
      //   className: 'bg-slate-800 text-white'
      // },
      {
        title: t("subtitleTable.translation"),
        key: 'translatedText',
        className: 'bg-slate-800 text-white'
      },
      // {
      //   title: 'Voice',
      //   key: 'voice',
      //   width: 150,
      //   className: 'bg-slate-800 text-white'
      // },
      // {
      //   title: t("subtitleTable.statusTranslation"),
      //   key: 'status',
      //   width: 110,
      //   className: 'bg-slate-800 text-white text-center'
      // },
      {
        title: t("subtitleTable.action"),
        key: 'action',
        width: 90,
        className: 'bg-slate-800 text-white text-right'
      }
    ];

    // Batches with errors
    const errorBatches = computed(() => {
      const result = [];
      const processedBatchIndexes = new Set();

      // Process all subtitles to find error batches
      for (const subtitle of props.subtitles) {
        if (subtitle.status !== "error") continue;

        const batchIndex = Math.floor((subtitle.id - 1) / props.batchSize);

        // Skip if we already processed this batch
        if (processedBatchIndexes.has(batchIndex)) continue;
        processedBatchIndexes.add(batchIndex);

        // Find all subtitles in this batch
        const batchItems = props.subtitles.filter(item => {
          const itemBatchIndex = Math.floor((item.id - 1) / props.batchSize);
          return itemBatchIndex === batchIndex;
        });

        result.push({
          batchIndex,
          items: batchItems,
          hasErrors: true
        });
      }

      return result;
    });

    // Filtered error batches with actual errors
    const errorBatchesWithErrors = computed(() => {
      return errorBatches.value.map(batch => {
        // Count actual errors (some may have been fixed)
        const errorItems = batch.items.filter(item => {
          const subtitle = props.subtitles.find(s => s.id === item.id);
          return subtitle && subtitle.status === "error";
        });

        const errorCount = errorItems.length;

        if (errorCount === 0) return null;

        const firstId = batch.items[0]?.id;
        const lastId = batch.items[batch.items.length - 1]?.id;
        const actualBatchIndex = Math.floor((firstId - 1) / props.batchSize);

        return {
          actualBatchIndex,
          firstId,
          lastId,
          errorCount
        };
      }).filter(Boolean);
    });

    // Count of error batches
    const errorBatchCount = computed(() => errorBatches.value.length);

    // Handle editing a subtitle
    const handleEdit = (id, text) => {
      editingId.value = id;
      editText.value = text;
    };
    const handleEdit1 = (id, text) => {
      editingId1.value = id;
      editText1.value = text;
    };

    // Save edited subtitle
    const handleSave = (id) => {
      props.onUpdateTranslation(id, editText.value);
      editingId.value = null;
      editText.value = '';
    };
    const handleSave1 = (id) => {
      props.onUpdateOriginalText(id, editText1.value);
      editingId1.value = null;
      editText1.value = '';
    };

    // Cancel editing
    const handleCancel = () => {
      editingId.value = null;
      editText.value = '';
    };
    const handleCancel1 = () => {
      editingId1.value = null;
      editText1.value = '';
    };

    // Handle row click
    const handleRowClick = (id) => {
      if (id !== editingId.value && props.onRetry && !props.translating) {
        if (state.currentPlayingSubtitleId !== id) {
          props.onRetry(id);
        }
      }
    };

    // Custom row props
    const customRow = (record) => {
      return {
        onClick: () => handleRowClick(record.id),
        'data-row-key': record.id
      };
    };
    // Thêm function scrollToSubtitle vào return của setup()
    const scrollToSubtitle = (subtitleId) => {
      if (!tableContainerRef.value || !subtitleId) return;

      // Tìm row element bằng class
      const targetRow = tableContainerRef.value.querySelector(`.subtitle-row-${subtitleId}`);

      if (targetRow) {
        // Scroll container đến vị trí của row, đặt row ở giữa màn hình
        const containerRect = tableContainerRef.value.getBoundingClientRect();
        const rowRect = targetRow.getBoundingClientRect();
        const containerScrollTop = tableContainerRef.value.scrollTop;

        // Tính toán vị trí scroll để row nằm ở giữa container
        const targetScrollTop = containerScrollTop +
          (rowRect.top - containerRect.top) -
          (containerRect.height / 2) +
          (rowRect.height / 2);

        tableContainerRef.value.scrollTo({
          top: Math.max(0, targetScrollTop),
          behavior: 'smooth'
        });
      }
    };
    // Get row class based on status
    const getRowClassName = (record, index) => {
      const batchIndex = Math.floor((record.id - 1) / props.batchSize);
      const isEven = index % 2 === 0;
      const isPlaying = record.id === state.currentPlayingSubtitleId;

      let className = isEven ? '-bg-slate-800' : '-bg-gray-50/50';

      if (retryingBatch.value === batchIndex && record.status === 'translating') {
        // className += ' bg-blue-50/70';
      }

      if (isPlaying) {
        className += ' bg-slate-700';
      }

      // className += ' hover:bg-blue-50/30 transition-colors border-b border-gray-100';
      className += ` subtitle-row-${record.id}`;

      if (editingId.value !== record.id) {
        className += ' cursor-pointer';
      }

      return className;
    };

    // Try to retry a batch of subtitles
    const handleRetryBatch = async (batchIndex) => {
      if (!props.onRetryBatch) return;

      console.log(`SubtitleTable: Retrying batch ${batchIndex}`);
      retryingBatch.value = batchIndex;

      try {
        // Check if batch exists in UI
        const batchExistsInUI = errorBatchesWithErrors.value.some(
          batch => batch.actualBatchIndex === batchIndex
        );

        if (!batchExistsInUI) {
          console.warn(`Batch ${batchIndex} no longer exists or has no errors in UI`);
          retryingBatch.value = null;
          return;
        }

        await props.onRetryBatch(batchIndex);
        console.log(`SubtitleTable: Successfully retried batch ${batchIndex}`);
      } catch (error) {
        console.error(`SubtitleTable: Error retrying batch ${batchIndex}:`, error);
      } finally {
        retryingBatch.value = null;
      }
    };

    // Handle pagination
    const goToPage = (page) => {
      console.log(`SubtitleTable: Going to page ${page}`);

      const validPage = Math.max(1, Math.min(page, totalPages.value));
      subtitleStore.setCurrentPage(validPage);

      // Scroll to top when changing page
      nextTick(() => {
        if (tableContainerRef.value) {
          tableContainerRef.value.scrollTo({ top: 0, behavior: 'smooth' });
        }
      });
    };

    // Handle scrolling to highlighted subtitle
    const scrollToHighlighted = () => {
      nextTick(() => {
        if (highlightedRowRef.value) {
          highlightedRowRef.value.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      });
    };

    // Handle suggesting translations
    const handleSuggestTranslation = async (id, originalText, currentTranslation) => {
      if (!props.onSuggestTranslation) return;

      suggestingId.value = id;
      loadingSuggestions.value = true;

      try {
        const aiSuggestions = await props.onSuggestTranslation(
          id,
          originalText,
          currentTranslation
        );

        // Parse the response if it's in code block format
        if (aiSuggestions.length === 1 && aiSuggestions[0].includes("```json")) {
          try {
            const jsonMatch = aiSuggestions[0].match(/```json\s*([\s\S]*?)\s*```/);
            if (jsonMatch && jsonMatch[1]) {
              const jsonData = JSON.parse(jsonMatch[1]);
              if (jsonData.translations && Array.isArray(jsonData.translations)) {
                suggestions.value = jsonData.translations;
                return;
              }
            }
          } catch (parseError) {
            console.error("Error parsing JSON in suggestion:", parseError);
          }
        }

        suggestions.value = aiSuggestions;
      } catch (error) {
        console.error("Error getting translation suggestions:", error);
        suggestions.value = [];
      } finally {
        loadingSuggestions.value = false;
      }
    };

    // Apply a suggestion
    const applyTranslationSuggestion = (suggestion) => {
      if (suggestingId.value === null) return;

      props.onUpdateTranslation(suggestingId.value, suggestion);
      closeSuggestions();
    };

    // Close suggestions
    const closeSuggestions = () => {
      suggestingId.value = null;
      suggestions.value = [];
    };

    // Get label type for suggestions
    const getLabelType = (idx) => {
      return idx === 0 ? "common" : idx === 1 ? "academic" : "creative";
    };

    // Get label text for suggestions
    const getLabelText = (idx) => {
      return idx === 0 ? "Thông dụng" : idx === 1 ? "Học thuật" : "Sáng tạo";
    };

    // Toggle table expansion
    const setExpandedTable = (value) => {
      expandedTable.value = value;
    };

    // Watch for changes in subtitles length
    watch(() => props.subtitles.length, (newLength) => {
      // Reset to page 1 if subtitles change significantly
      if (newLength > 0 && currentPage.value > Math.ceil(newLength / rowsPerPage)) {
        subtitleStore.setCurrentPage(1);
      }
    });

    // Watch for highlighted subtitle changes to scroll to it
    watch(() => state.currentPlayingSubtitleId, (newId) => {
      if (newId) {
        // Check if the highlighted subtitle is on the current page
        const isOnCurrentPage = currentPageSubtitles.value.some(s => s.id === newId);
        if (!isOnCurrentPage) {
          // Find which page this subtitle is on
          const index = props.subtitles.findIndex(s => s.id === newId);
          if (index !== -1) {
            const targetPage = Math.floor(index / rowsPerPage) + 1;
            goToPage(targetPage);
          }
        }

        scrollToHighlighted();
      }
    }, { immediate: true });

    watch(() => state.currentPlayingSubtitleId, (newId, oldId) => {
      if (newId && newId !== oldId) {
        // Tìm subtitle trong danh sách hiện tại
        const subtitleIndex = props.subtitles.findIndex(s => s.id === newId);

        if (subtitleIndex !== -1) {
          // Tính toán trang chứa subtitle này
          const targetPage = Math.floor(subtitleIndex / rowsPerPage) + 1;

          // Nếu subtitle không ở trang hiện tại, chuyển đến trang đó
          if (targetPage !== currentPage.value) {
            subtitleStore.setCurrentPage(targetPage);
          }

          // Scroll đến subtitle sau khi DOM đã cập nhật
          nextTick(() => {
            scrollToSubtitle(newId);
          });
        }
      }
    }, {
      immediate: true // Chạy ngay lập tức khi component mount
    });

    function handlePlayVideo(record) {
      if (props.onPlayVideo) {
        props.onPlayVideo(record);
      }
    }
    function toggleEnabledForSubtitle(id) {
      const subtitle = props.subtitles.find(s => s.id === id);
      if (subtitle) {
        subtitle.isEnabled = !subtitle.isEnabled;
      }
    }
    async function handleReText(record) {
      console.log('handleReText', record);
      isLoading.value[record.id] = true;
      // state.currentTime
      // screenshotOvr
      const videoPath = ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')
      const cloned = JSON.parse(JSON.stringify({
        videoPath,
        fromSecond: state.currentTime,
        cropData: state.cropData
      }));
      const res = await electronAPI.getTextFromFrameVideo(cloned)
        .catch(err => {
          console.error('Error getting text from frame video:', err);
          message.error('Error getting text from frame video: ' + err.message, 5);
          isLoading.value[record.id] = false;
          return null;
        });
      console.log('res', res);
      if (!res?.text) {
        message.error('Không lấy được đoạn text từ frame video', 5);
        isLoading.value[record.id] = false;
        return;
      } else {
        state.cropText = res.text;
      }


      const confirm = await new Promise((resolve) => {
        Modal.confirm({
          title: 'Xác nhận',
          content: `Lấy đoạn này: ${res?.text}?`,
          okText: 'Có',
          cancelText: 'Không',
          onOk: () => resolve(true),
          onCancel: () => resolve(false),
        });
      });

      if (confirm && res?.text) {
        record.text = res.text;
        console.log('record', record);
      }
      isLoading.value[record.id] = false;
    }
    async function handleDelete(id) {
      const confirm = await new Promise((resolve) => {
        Modal.confirm({
          title: 'Xác nhận',
          content: 'Bạn có chắc chắn muốn xóa subtitle này?',
          okText: 'Có',
          cancelText: 'Không',
          onOk: () => resolve(true),
          onCancel: () => resolve(false),
        });
      });

      if (confirm) {
        props.onDelete(id);
      }
    }

    // Handle insert subtitle before current row
    const handleInsertBefore = (record) => {
      insertTargetId.value = record.id;
      insertTargetSubtitle.value = record;
      insertPosition.value = 'before';
      showInsertModal.value = true;
    };

    // Handle insert subtitle after current row
    const handleInsertAfter = (record) => {
      insertTargetId.value = record.id;
      insertTargetSubtitle.value = record;
      insertPosition.value = 'after';
      showInsertModal.value = true;
    };

    // Handle insert confirm from modal
    const handleInsertConfirm = (newSubtitleData) => {
      if (!props.onInsert) return;

      const newSubtitle = insertSubtitle(props.subtitles, insertTargetId.value, newSubtitleData.position);
      if (newSubtitle) {
        // Update with user data
        newSubtitle.start = newSubtitleData.startTime;
        newSubtitle.end = newSubtitleData.endTime;
        newSubtitle.startTime = parseTimeToSeconds(newSubtitleData.startTime);
        newSubtitle.endTime = parseTimeToSeconds(newSubtitleData.endTime);
        newSubtitle.text = newSubtitleData.text;
        newSubtitle.translatedText = newSubtitleData.translatedText;

        props.onInsert(newSubtitle, insertTargetId.value, newSubtitleData.position, newSubtitleData.adjustTiming);
      }
    };

    // Handle split subtitle
    const handleSplit = (record) => {
      splitTargetSubtitle.value = record;
      showSplitModal.value = true;
    };

    // Handle split confirm from modal
    const handleSplitConfirm = (splitData) => {
      if (!props.onSplit || !splitTargetSubtitle.value) return;

      const record = splitTargetSubtitle.value;
      const splitTime = parseTimeToSeconds(splitData.splitTime);

      const firstPart = {
        ...record,
        end: splitData.splitTime,
        endTime: splitTime,
        text: splitData.part1.text,
        translatedText: splitData.part1.translatedText
      };

      const secondPart = {
        ...record,
        id: record.id + 1,
        index: record.index + 1,
        start: splitData.splitTime,
        startTime: splitTime,
        text: splitData.part2.text,
        translatedText: splitData.part2.translatedText,
        // Reset audio generation status
        isGenerated: false,
        isGenerated1: false,
        isGenerated2: false,
        isGenerated3: false,
        audioUrl: '',
        audioUrl1: '',
        audioUrl2: '',
        audioUrl3: '',
        duration: 0
      };

      props.onSplit(record.id, [firstPart, secondPart]);
    };

    // Handle merge subtitle
    const handleMerge = (record) => {
      if (!props.onMerge) return;

      const nextSubtitle = props.subtitles.find(s => s.id === record.id + 1);
      if (!nextSubtitle) {
        message.warning('Không có subtitle tiếp theo để hợp nhất');
        return;
      }

      Modal.confirm({
        title: 'Hợp nhất subtitle',
        content: `Bạn có muốn hợp nhất subtitle ${record.id} với ${nextSubtitle.id}?`,
        okText: 'Có',
        cancelText: 'Không',
        onOk: () => {
          const mergedSubtitle = mergeSubtitles(record, nextSubtitle);
          props.onMerge([record.id, nextSubtitle.id], mergedSubtitle);
        }
      });
    };

    return {
      t,
      state,
      columns,
      currentPage,
      totalPages,
      editingId,
      editingId1,
      editText,
      editText1,
      retryingBatch,
      expandedTable,
      tableContainerRef,
      highlightedRowRef,
      suggestingId,
      suggestions,
      loadingSuggestions,
      currentPageSubtitles,
      errorBatchCount,
      errorBatchesWithErrors,
      isLoading,
      handleEdit,
      handleEdit1,
      handleSave,
      handleSave1,
      handleCancel,
      handleCancel1,
      handleRetryBatch,
      handleRowClick,
      handleSuggestTranslation,
      applyTranslationSuggestion,
      closeSuggestions,
      getLabelType,
      getLabelText,
      getRowClassName,
      customRow,
      goToPage,
      setExpandedTable,
      handlePlayVideo,
      toggleEnabledForSubtitle,
      handleReText,
      handleDelete,
      handleInsertBefore,
      handleInsertAfter,
      handleInsertConfirm,
      handleSplit,
      handleSplitConfirm,
      handleMerge,
      scrollToSubtitle,
      // Modal states
      showInsertModal,
      showSplitModal,
      insertTargetId,
      insertTargetSubtitle,
      insertPosition,
      splitTargetSubtitle
    };
  }
})
</script>

<style scoped>
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.4);
}

.suggestion-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 40%;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 90%;
}

/* Custom scrollbar styling để match với dark theme */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #6B7280;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
</style>
