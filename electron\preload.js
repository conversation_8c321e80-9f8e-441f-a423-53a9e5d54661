const { contextBridge, ipc<PERSON>enderer } = require('electron');
const fs = require('fs');
const path = require('path');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // TTS API
  generateTTS: async (data) => {
    return ipcRenderer.invoke('generate-tts', data);
  },

  // Get speakers list
  getSpeakers: (ttsEngine) => {
    return ipcRenderer.invoke('get-speakers', ttsEngine);
  },

  // SRT file handling
  processSRT: (data) => {
    return ipcRenderer.invoke('process-srt', data);
  },

  // File system operations
  saveFile: (filePath, content) => {
    return ipcRenderer.invoke('save-file', { filePath, content });
  },

  readFile: ({filePath}) => {
    return ipcRenderer.invoke('read-file', { filePath });
  },

  // Dialog operations
  openFileDialog: (options) => {
    return ipcRenderer.invoke('open-file-dialog', options);
  },

  saveFileDialog: (options) => {
    return ipcRenderer.invoke('save-file-dialog', options);
  },

  // Audio processing with FFmpeg
  processAudio: (inputFile, outputFile, options) => {
    return ipcRenderer.invoke('process-audio', { inputFile, outputFile, options });
  },

  // Concatenate audio files
  concatenateAudio: (inputFiles, outputFile, timings) => {
    return ipcRenderer.invoke('concatenate-audio', { inputFiles, outputFile, timings });
  },

  // Run FFmpeg command
  runFFmpeg: (fileListPath, outputPath) => {
    return ipcRenderer.invoke('run-ffmpeg', fileListPath, outputPath);
  },
  mergeAudioFiles: (audioFiles, outputPath, subs, pathFile) => {
    return ipcRenderer.invoke('merge-audio-files', { audioFiles, outputPath, subs,pathFile });
  },

  // Create temporary directory
  createTempDir: (dirName) => {
    return ipcRenderer.invoke('create-temp-dir', dirName);
  },

  // Download file from URL
  downloadFile: (url, filePath) => {
    return ipcRenderer.invoke('download-file', url, filePath);
  },

  // Write file
  writeFile: (filePath, content) => {
    return ipcRenderer.invoke('write-file', filePath, content);
  },

  // Open file with default application
  openFile: (filePath) => {
    return ipcRenderer.invoke('open-file', filePath);
  },

  // Get app paths
  getAppPath: (name) => {
    return ipcRenderer.invoke('get-app-path', name);
  },
  //
  openFolder: (folderPath) => {
    return ipcRenderer.invoke('open-folder', folderPath);
  },
  setSession: (session) => {
    return ipcRenderer.invoke('set-session', session);
  },
  getSession: () => {
    return ipcRenderer.invoke('get-session');
  },
  // Listen for events
  on: (channel, callback) => {
    const subscription = (_event, ...args) => callback(...args);
    ipcRenderer.on(channel, subscription);

    return () => {
      ipcRenderer.removeListener(channel, subscription);
    };
  },
  removeListener: (channel, callback) => {
    ipcRenderer.removeListener(channel, callback);
  },
  setCurrentDir: (dirPath) => {
    return ipcRenderer.invoke('set-current-dir', dirPath);
  },
  getCurrentDir: () => {
    return ipcRenderer.invoke('get-current-dir');
  },

  // Video speed adjustment
  adjustSpeed: (options) => {
    return ipcRenderer.invoke('adjust-speed', options);
  },

  // Process management
  stopProcess: (processId) => {
    return ipcRenderer.invoke('stop-process', processId);
  },

  getActiveProcesses: () => {
    return ipcRenderer.invoke('get-active-processes');
  },

  // Video to WAV conversion
  convertVideoToWav: (options) => {
    return ipcRenderer.invoke('convert-video-to-wav', options);
  },

  // Whisper audio processing
  processAudioWhisper: (options) => {
    return ipcRenderer.invoke('process-audio-whisper', options);
  },

  // Video OCR processing
  processVideoOcr: (options) => {
    return ipcRenderer.invoke('process-video-ocr', options);
  },

  // Run OCR batch script
  runOcrBatch: (options) => {
    return ipcRenderer.invoke('run-ocr-batch', options);
  },

  // Video cutting functions
  getVideoDuration: (videoPath) => {
    return ipcRenderer.invoke('get-video-duration', videoPath);
  },

  cutVideoSegment: (options) => {
    return ipcRenderer.invoke('cut-video-segment', options);
  },

  splitVideoIntoParts: (options) => {
    return ipcRenderer.invoke('split-video-into-parts', options);
  },

  // Video rendering with SRT
  renderVideoWithSrt: (options) => {
    return ipcRenderer.invoke('render-video-with-srt', options);
  },

  // Event listeners for video rendering progress
  onRenderVideoProgress: (callback) => {
    return ipcRenderer.on('render-video-progress', callback);
  },

  removeRenderVideoProgress: (callback) => {
    return ipcRenderer.removeListener('render-video-progress', callback);
  },
  processSrtAndVideo: (options) => {
    return ipcRenderer.invoke('process-srt-and-video', options);
  },
  processSRTAndRender: (srtArray, videoPath) => {
    return ipcRenderer.invoke('process-srt-and-render', srtArray, videoPath);
  },
  processVideoSimplified: (srtArray, videoPath, options={}) => {
    return ipcRenderer.invoke('process-video-simplified', srtArray, videoPath,options);
  },
  demucs: (options) => {
    return ipcRenderer.invoke('demucs', options);
  },
  processVideoWithOptions: (options) => {
    return ipcRenderer.invoke('process-video-with-options', options);
  },
  getTextFromFrameVideo: (data) => {
    return ipcRenderer.invoke('get-text-from-frame-video', data);
  },
  getVideoInfo: (filePath) => {
    return ipcRenderer.invoke('get-video-info', filePath);
  }
});
