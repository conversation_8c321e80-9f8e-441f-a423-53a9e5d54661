<template>
  <!-- Container với layout pattern chuẩn -->
  <div class="flex-1 flex flex-col bg-gray-800 text-white min-h-0">
    <!-- Content area - có thể scroll -->
    <div class="flex-1 min-h-0 overflow-auto p-4">
      <div class="space-y-4">
      <div>
        <label
          for="srt-table-file"
          class="block text-sm font-medium text-gray-300"
        >
          SRT File
        </label>
        <div class="mt-1 flex items-center space-x-2 gap-1">
          <input
            type="file"
            id="srt-table-file"
            accept=".srt"
            @change="handleFileUpload"
            class="hidden"
          />
          <a-button @click.prevent="openFileInput" :loading="isProcessing">
            Import New SRT File
          </a-button>

          <!-- SRT Lists Modal -->
          <SrtLists
            buttonText="Select Existing SRT"
            @select="handleSelectSrt"
            @import-new="handleImportNew"
          />
          <a-button @click="resetFileSelected" :disabled="!srtFile"> Reset </a-button>

          <span v-if="srtFile" class="ml-2 text-sm text-gray-400">
            Current file: {{ srtFile.name }}
          </span>

          <div class="flex-1"></div>
          <!-- Terminology Dictionary Editor Button -->
          <TerminologyDictionaryEditor
            :buttonText="t('terminologyDictionary.edit')"
            @update="handleTerminologyUpdate"
          />

          <!-- xây dựng từ điển thuật ngữ -->
          <a-button
            @click="ensureTerminologyDictionary"
            danger
            :disabled="srtItems.length === 0"
            :loading="termIsBuilding"
          >
            Thuật ngữ
          </a-button>

          <!-- Main Translation Button -->
          <a-button
            @click="handleTranslateWithSrtServiceTerm"
            type="primary"
            :disabled="srtItems.length === 0"
            :loading="translating"
          >
            Dịch
          </a-button>

          <!-- Continue Translation Button - Only shown when there are partially translated items -->
          <a-button
            v-if="hasPartialTranslation"
            @click="handleContinueTranslation"
            type="primary"
            :disabled="srtItems.length === 0"
          >
            {{ t('terminologyDictionary.continueTranslation') }}
          </a-button>
        </div>
        <div class="mt-2 flex items-center space-x-2 gap-1">
          <!-- Voice Configuration Button -->
          <a-button
            @click="showVoiceConfigDrawer"
            :disabled="srtItems.length === 0"
          >
            {{ t('voiceConfig.title') }}
          </a-button>

          <!-- Apply Voice Buttons -->
          <a-dropdown :disabled="srtItems.length === 0">
            <a-button>
              {{ t('voiceConfig.selectAll') }}
              <down-outlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="applyVoiceToAll(1)">
                  {{ t('voiceConfig.voice1') }}
                </a-menu-item>
                <a-menu-item @click="applyVoiceToAll(2)">
                  {{ t('voiceConfig.voice2') }}
                </a-menu-item>
                <a-menu-item @click="applyVoiceToAll(3)">
                  {{ t('voiceConfig.voice3') }}
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>

          <!-- Generate Audio Buttons -->
          <a-dropdown :disabled="srtItems.length === 0 || generatingAudio">
            <a-button :loading="generatingAudio">
              {{ t('voiceConfig.generateAudio') }}
              <down-outlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="generateAllAudio(true)">
                  {{ t('voiceConfig.generateForTranslated') }}
                </a-menu-item>
                <a-menu-item @click="generateAllAudio(false)">
                  {{ t('voiceConfig.generateForOriginal') }}
                </a-menu-item>
                <a-menu-item @click="generateAllAudioIsNotGenerated(true)">
                  {{ t('voiceConfig.generateForTranslatedNotGenerated') }}
                </a-menu-item>
                <a-menu-item @click="generateAllAudioIsNotGenerated(false)">
                  {{ t('voiceConfig.generateForOriginalNotGenerated') }}
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>

          <!-- Join Audio Files Button -->
          <!-- <a-button
            @click="joinMp3FilesV2"
            :disabled="srtItems.length === 0 || joiningAudio"
            :loading="joiningAudio"
          >
            {{ t('voiceConfig.joinAudios') }}
          </a-button> -->
          <RenderVideoButton :key="key" />

          <!-- apply is enabled all -->
          <a-checkbox
            :checked="allEnabled"
            @change="toggleAllEnabled"
            :indeterminate="indeterminate"
          >
            {{ allEnabled ? 'Unselect All' : 'Select All' }}
          </a-checkbox>
          <!-- srt export -->
          <a-button
            @click="exportSrt"
            :disabled="srtItems.length === 0"
          >
            Export SRT
          </a-button>
        </div>
      </div>
        <div v-if="validationError" class="mt-4 p-3 bg-red-900/50 text-red-300 rounded border border-red-700">
          {{ validationError }}
        </div>

      <div className="pt-2 border-t" v-if="translating">
        <div className="w-full" v-if="isPaused">
          <div className="flex items-center justify-between mb-1">
            <div className="text-sm font-medium text-amber-600">
              {{ t("translationSettings.translationPaused") }}
            </div>
            <div className="text-sm text-gray-500">{{ translationProgress }}%</div>
          </div>
          <LoadingIndicator :progress="translationProgress" :isPaused="isPaused" />
          <div
            className="mt-2 text-xs text-amber-600 px-2 py-1 bg-amber-50 border border-amber-100 rounded-md"
          >
            {{ t("translationSettings.translationPaused") }}
          </div>
        </div>
        <div className="w-full" v-else>
          <div className="flex items-center justify-between mb-1">
            <div className="text-sm font-medium text-blue-600">
              {{ t("translationSettings.translationInProgress") }}
            </div>
            <div className="text-sm text-gray-500">{{ translationProgress }}%</div>
          </div>
          <LoadingIndicator :progress="translationProgress" />
          <div className="mt-2 text-xs text-gray-600 flex items-center justify-between">
            <span>
              <!-- {{modelTranslations.title[locale === 'en' ? 'en' : 'vi']}}: -->
              <span className="font-medium ml-1">
                {{
                  AVAILABLE_MODELS.find((m) => m.id === ttsStore.model)?.name || ttsStore.model
                }}
              </span>
            </span>
            <span className="text-gray-500">{{
              translationProgress > 0 && translationProgress < 100
                ? `${Math.round((srtItems.length * translationProgress) / 100)}/${
                    srtItems.length
                  }`
                : ""
            }}</span>
          </div>
        </div>
      </div>
      <SubtitleTable
        v-if="srtItems.length > 0"
        :subtitles="srtItems"
        @retry="handleRetrySubtitle"
        @updateTranslation="handleUpdateSubtitle"
        @updateOriginalText="handleUpdateOriginalText"
        :translating="translating"
        :batchSize="BATCH_SIZE"
        :highlightedSubtitleId="state.currentPlayingSubtitleId"
        @suggestTranslation="handleSuggestBetterTranslation"
        :isVoiceSelectedForSubtitle="isVoiceSelectedForSubtitle"
        :toggleVoiceForSubtitle="toggleVoiceForSubtitle"
        :generateAudioForSubtitle="generateAudioForSubtitle"
        :generatingAudio="generatingAudio"
        :currentGeneratingId="state.currentPlayingSubtitleId"
        @playVideo="handlePlayVideo"
        @delete="handleDelete"
        @insert="handleInsert"
        @split="handleSplit"
        @merge="handleMerge"
        @reorder="handleReorder"
      />
        <div
          v-else-if="!isProcessing && !srtFile"
          class="text-center py-8 text-gray-400"
        >
          <p class="mb-4">No SRT file selected.</p>
        <div class="flex justify-center space-x-4 mb-2">
          <a-button @click="openFileInput">Import New SRT File</a-button>
          <SrtLists
            buttonText="Select Existing SRT"
            @select="handleSelectSrt"
            @import-new="handleImportNew"
          />
        </div>
        <DragDropUpload
          accept=".srt,application/x-subrip,text/srt"
          :max-size="100 * 1024 * 1024"
          @files-selected="handleFileUpload"
        />
      </div>
    </div>

    <!-- Voice Configuration Drawer -->
    <VoiceConfigDrawer
      :visible="voiceConfigDrawerVisible"
      @close="closeVoiceConfigDrawer"
      @apply="applyVoiceConfig"
    />

    <!-- Audio Generation Progress -->
    <div v-if="generatingAudio || joiningAudio" class="fixed bottom-4 right-4 bg-gray-800 shadow-lg rounded-lg p-4 z-50 w-80 border border-gray-700">
      <div class="flex justify-between items-center mb-2">
        <h3 class="text-md font-medium text-white">{{ t('voiceConfig.processing') }}</h3>
        <span class="text-white">{{ audioProgress }}%</span>
      </div>
      <a-progress :percent="audioProgress" :status="audioProgress === 100 ? 'success' : 'active'" />
    </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted } from "vue";
import { message } from "ant-design-vue";
import { parseSRT } from "@/lib/utils";
import { getProviderForModel } from "@/lib/modelUtils";
import SubtitleTable from "./SubtitleTable.vue";
import SrtLists from "./SrtLists.vue";
import DragDropUpload from "./DragDropUpload.vue";
import { useTTSStore } from "@/stores/ttsStore";
import { useSRTStore } from "@/stores/srtStore";
import { useI18n } from "@/i18n/i18n";
import { hasApiKey, setApiKey } from "@/lib/apiKeyManager";
import LoadingIndicator from "./LoadingIndicator.vue";
import { AVAILABLE_MODELS } from "@/lib/modelUtils";
import { translateSrtService, configureTranslateService, buildTerminologyDictionary } from "@/lib/allTranslateService";
import TerminologyDictionaryEditor from "./TerminologyDictionaryEditor.vue";
import VoiceConfigDrawer from "./VoiceConfigDrawer.vue";
import { translateText } from "@/lib/translationService";
import { DownOutlined } from '@ant-design/icons-vue';
// import VideoPlayer from "./VideoPlayer.vue";
import RenderVideoButton from "./RenderVideoButton.vue";
import { state } from '@/lib/state';
import { useSubtitleStore } from '@/stores/subtitle-store';
import {
  reorderSubtitleIds,
  adjustSubtitleTimes,
  mergeSubtitles,
  fixOverlappingSubtitles
} from '@/lib/subtitleUtils';

const ttsStore = useTTSStore();
const srtStore = useSRTStore();
const subtitleStore = useSubtitleStore();
const { t } = useI18n();

const BATCH_SIZE = 10;
const translating = ref(false);
const srtFile = ref(null);
const isProcessing = ref(false);
const srtItems = ref([]);
const selectedItems = ref([]);
const currentPage = ref(1);
const defaultSpeaker = computed(() => ttsStore.selectedSpeaker);
const validationError = ref(null);
const pauseStateRef = ref(false);
const translationError = ref(null);
const translationProgress = ref(0);
const isPaused = ref(false);
const existingTranslations = ref({});
const lastTranslatedIndex = ref(0);
const apiKeyProvided = ref(false);
const termIsBuilding = ref(false);
const playCurrentTime = ref(0);
const allEnabled = ref(true);
const indeterminate = ref(false);

// new key for reopen component
const key = ref(0);




// Voice configuration
const voiceConfigDrawerVisible = ref(false);
const generatingAudio = ref(false);
const joiningAudio = ref(false);
const audioProgress = ref(0);
const videoPlayer = ref(null);
const selectedVoiceConfig = ref({
  language: 'Tiếng Việt',
  voice1: {
    enabled: true,
    speaker: 'tts.other.BV562_streaming',
    speed: 2,
    volume: 0,
    pitch: 0,
    rate: 1,
    trim: 0.1
  },
  voice2: {
    enabled: false,
    speaker: 'tts.other.BV074_streaming',
    speed: 2,
    volume: 0,
    pitch: 0,
    rate: 1,
    trim: 0.1
  },
  voice3: {
    enabled: false,
    speaker: 'tts.other.BV075_streaming',
    speed: 2,
    volume: 0,
    pitch: 0,
    rate: 1,
    trim: 0.1
  },
  masterVolume: 100
});

// Selected voice for each subtitle
const selectedVoices = ref({});

// Computed property to check if there are partially translated items
const hasPartialTranslation = computed(() => {
  if (!srtItems.value || srtItems.value.length === 0) return false;

  const translatedCount = srtItems.value.filter(item =>
    item.status === "translated" && item.translatedText
  ).length;

  return translatedCount > 0 && translatedCount < srtItems.value.length;
});


function handleUpdateSubtitle(id, text) {
  // Update subtitle logic
  const index = srtItems.value.findIndex((s) => s.id === id);
  if (index !== -1) {
    srtItems.value[index].translatedText = text;
    srtItems.value[index].status = "translated";
  }
}

function handleUpdateOriginalText(id, text) {
  // Update original text logic
  const index = srtItems.value.findIndex((s) => s.id === id);
  if (index !== -1) {
    srtItems.value[index].text = text;
  }
}

onMounted(async () => {
  if (ttsStore.model.includes("gemini")) {
    apiKeyProvided.value = hasApiKey(ttsStore.model);
    setApiKey(ttsStore.geminiKey, ttsStore.model);
  }
  if (ttsStore.model.includes("openai")) {
    apiKeyProvided.value = hasApiKey(ttsStore.model);
    setApiKey(ttsStore.openaiKey, ttsStore.model);
  }
  if (ttsStore.model.includes("deepseek")) {
    apiKeyProvided.value = hasApiKey(ttsStore.model);
    setApiKey(ttsStore.deepseekKey, ttsStore.model);
  }
  if(ttsStore.model){
  let apiKey = '';
  const provider = getProviderForModel(ttsStore.model);

  if (provider === 'deepseek') {
    apiKey = ttsStore.deepseekKey;
  } else if (provider === 'gemini') {
    apiKey = ttsStore.geminiKey;
  } else if (provider === 'openai') {
    apiKey = ttsStore.openaiKey;
  }
  console.log("API key provided:", apiKey, ttsStore.model);

  // Configure the translate service
  configureTranslateService(apiKey, ttsStore.model);
  }
});

// Watch for changes to srtItems and update the current SRT list in the store
watch(
  srtItems,
  (newItems) => {
    if (ttsStore.currentSrtList && newItems.length > 0) {
      // Find the index of the current SRT list
      const index = ttsStore.srtLists.findIndex(
        (item) => item.name === ttsStore.currentSrtList.name
      );
      if (index !== -1) {
        // Update the items in the store
        ttsStore.srtLists[index].items = [...newItems];
      }
    }
  },
  { deep: true }
);

watch(
  () => ttsStore.activeTab,
  async (newVal) => {
    if (newVal === "srt-table") {
      await handleMounded();
    }
  }
);

// split
const handleSplit = (id, splitSubtitles) => {
  console.log('Split subtitle:', id, splitSubtitles);

  const index = srtItems.value.findIndex(s => s.id === id);
  if (index === -1) return;

  // Replace original subtitle with split parts
  srtItems.value.splice(index, 1, ...splitSubtitles);

  // Reorder IDs
  srtItems.value = reorderSubtitleIds(srtItems.value);

  message.success(`Split subtitle ${id} into 2 parts`);
};

// handleMerge
const handleMerge = (ids, mergedSubtitle) => {
  console.log('Merge subtitles:', ids, mergedSubtitle);

  // Find indices of subtitles to merge
  const indices = ids.map(id => srtItems.value.findIndex(s => s.id === id)).sort((a, b) => b - a);

  if (indices.some(i => i === -1)) return;

  // Remove the subtitles (in reverse order to maintain indices)
  indices.forEach(index => {
    srtItems.value.splice(index, 1);
  });

  // Insert merged subtitle at the first position
  srtItems.value.splice(Math.min(...indices), 0, mergedSubtitle);

  // Reorder IDs
  srtItems.value = reorderSubtitleIds(srtItems.value);

  message.success(`Merged subtitles ${ids.join(', ')}`);
};

// handleInsert
const handleInsert = (newSubtitle, targetId, position, adjustTiming = true) => {
  console.log('Insert subtitle:', newSubtitle, targetId, position, adjustTiming);

  const targetIndex = srtItems.value.findIndex(s => s.id === targetId);
  if (targetIndex === -1) return;

  const insertIndex = position === 'before' ? targetIndex : targetIndex + 1;

  // Insert new subtitle
  srtItems.value.splice(insertIndex, 0, newSubtitle);

  // Reorder IDs
  srtItems.value = reorderSubtitleIds(srtItems.value);

  // Adjust timing if requested
  if (adjustTiming) {
    const insertDuration = newSubtitle.endTime - newSubtitle.startTime;
    srtItems.value = adjustSubtitleTimes(srtItems.value, insertIndex + 1, insertDuration);
  }

  // Fix any overlapping srtItems
  srtItems.value = fixOverlappingSubtitles(srtItems.value);

  message.success(`Inserted new subtitle ${position} subtitle ${targetId}`);
};

// handleReorder
const handleReorder = (newOrder) => {
  console.log('Reorder subtitles:', newOrder);
  srtItems.value = reorderSubtitleIds(newOrder);
  message.success('Reordered subtitles');
};

const handleDelete = (id) => {
  console.log('Delete subtitle:', id);
  const index = srtItems.value.findIndex(s => s.id === id);
  if (index !== -1) {
    srtItems.value.splice(index, 1);
    // Reorder IDs
    srtItems.value = reorderSubtitleIds(srtItems.value);
    message.success(`Deleted subtitle ${id}`);
  }
};

async function handleMounded() {
  if (srtStore.srtContent && srtStore.srtFile) {
    if (ttsStore.currentSrtList?.path === srtStore.srtFile?.path) return;
    await processSrtFile(srtStore.srtContent);
  }
}

async function processSrtFile(content) {
  try {
    isProcessing.value = true;
    // Parse SRT content
    const parsedItems = parseSRT(content);

    // Add additional properties for voice selection and speed
    srtItems.value = parsedItems.map((item) => ({
      ...item,
      translatedText: "",
      status: "pending",
      selectedSpeaker: defaultSpeaker.value,
      speechRate: 0, // Default speech rate (normal speed)
      audioUrl: "",
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1
    }));

    srtFile.value = srtStore.srtFile || srtFile.value;

    // Create a new SRT list entry
    const newSrtList = {
      name: srtFile.value.name + "_" + new Date().getTime(),
      items: [...srtItems.value],
      path: srtFile.value.path,
    };

    // Add to the store
    ttsStore.srtLists.push(newSrtList);

    // Set as current SRT list
    ttsStore.currentSrtList = newSrtList;
    await electronAPI.setCurrentDir(srtFile.value.path);

    message.success(`Imported ${parsedItems.length} items from SRT file`);
    ttsStore.terminologyDictionary = {};
    key.value++;
  } catch (error) {
    console.error("Error processing SRT file:", error);
    message.error("Error processing SRT file: " + error.message);
  } finally {
    isProcessing.value = false;
  }
}

// Function to handle file upload
async function handleFileUpload(e) {
  const file = e?.target?.files?.[0] || e?.[0];
  if (!file) return;

  srtFile.value = file;
  isProcessing.value = true;

  try {
    // Read the SRT file
    const reader = new FileReader();
    const content = await new Promise((resolve, reject) => {
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = (e) => reject(e);
      reader.readAsText(file);
    });

    // Parse SRT content
    await processSrtFile(content);

    message.success(`Imported ${srtItems.value.length} items from SRT file`);
  } catch (error) {
    console.error("Error processing SRT file:", error);
    message.error("Error processing SRT file: " + error.message);
  } finally {
    isProcessing.value = false;
  }
}

// reset file selected
async function resetFileSelected() {
  srtFile.value = null;
  srtItems.value = [];
  selectedItems.value = [];
  currentPage.value = 1;
  ttsStore.currentSrtList = null;
  await electronAPI.setCurrentDir();
}

function openFileInput() {
  document.getElementById("srt-table-file").click();
}

function handleSelectSrt(srtList) {
  if (!srtList || !srtList.items || srtList.items.length === 0) {
    message.error("Selected SRT file has no items");
    return;
  }

  // Reset current state
  srtItems.value = [];
  selectedItems.value = [];
  currentPage.value = 1;

  // Set the selected SRT file items
  srtItems.value = [...srtList.items];

  // Set the file name for display
  const fileName = srtList.name.split("_")[0];
  srtFile.value = { name: fileName };
  key.value++;

  message.success(`Loaded ${srtItems.value.length} items from selected SRT file`);
}

function handleImportNew() {
  openFileInput();
}

// function hasApiKey(model) {
//   return !!ttsStore.model && ttsStore.model === model;
// }

// handle translate
// Xác thực đầu vào trước khi dịch
const validateBeforeTranslate = () => {
  // Kiểm tra API key cho model hiện tại
  if (!hasApiKey(ttsStore.model)) {
    const provider = getProviderForModel(ttsStore.model);
    validationError.value =
      t("errors.apiKeyRequired") + (provider ? ` (${provider})` : "");
    return false;
  }

  // Kiểm tra file SRT
  if (!srtFile.value || srtItems.value.length === 0) {
    validationError.value = t("errors.fileRequired");
    return false;
  }

  // Nếu tất cả đều hợp lệ, xóa thông báo lỗi
  validationError.value = null;
  return true;
};




    // Handle retrying a subtitle or navigating to it
    const handleRetrySubtitle = async (id) => {
        // Check if this is just a click to navigate to this subtitle
        if (id === state.currentPlayingSubtitleId) {
            return; // Already selected, no need to retry translation
        }

        // Set the current playing subtitle immediately for navigation purposes
        state.currentPlayingSubtitleId = id;
        subtitleStore.activeSubtitleId = id;
        // Only proceed with retry if the status is error
        const subtitleIndex = srtItems.value.findIndex(sub => sub.id === id);
        if (subtitleIndex === -1) return;

        const subtitle = srtItems.value[subtitleIndex];
        if (subtitle.status !== "error") {
            return; // Just navigation, not a retry
        }

        // If it's an actual retry (status is error), proceed with retry logic
        message.info("Retrying translation for subtitle #" + id);

        // Use the continue translation function to handle retries
        await handleContinueTranslation();
    };








// These functions have been removed as they're no longer needed

// Function to handle terminology dictionary updates
const handleTerminologyUpdate = (updatedDictionary) => {
  console.log("Terminology dictionary updated:", updatedDictionary);
  // The dictionary is already updated in the store by the TerminologyDictionaryEditor component
};

// Function to check if we need to build terminology dictionary
const ensureTerminologyDictionary = async () => {
  // If we already have a terminology dictionary with entries, use it
  if (Object.keys(ttsStore.terminologyDictionary || {}).length > 0) {
    return true;
  }
  termIsBuilding.value = true;
  // Otherwise, build a new one
  try {
    message.info(t("terminologyDictionary.buildingDictionary"));
    translationProgress.value = 5;

    // Get sample text from the first 150 subtitles
    const sampleTexts = srtItems.value.slice(0, 150).map(sub => sub.text);
    const sampleText = sampleTexts.join('\n\n');

    // Build terminology dictionary
    ttsStore.terminologyDictionary = await buildTerminologyDictionary(
      sampleText,
      'Chinese'
    );

    console.log("Built terminology dictionary:", ttsStore.terminologyDictionary);
    message.success(t("terminologyDictionary.dictionaryBuilt"));
    termIsBuilding.value = false;
    return true;
  } catch (error) {
    console.error("Error building terminology dictionary:", error);
    message.error("Error building terminology dictionary: " + error.message);
    termIsBuilding.value = false;
    return false;
  }
};

// Function to save translation progress
const saveTranslationProgress = () => {
  // Create a map of translated items
  const translations = {};
  let lastIndex = 0;

  srtItems.value.forEach((item, index) => {
    if (item.status === "translated" && item.translatedText) {
      translations[item.id] = item.translatedText;
      lastIndex = Math.max(lastIndex, index);
    }
  });

  // Save the last translated index
  lastTranslatedIndex.value = lastIndex;
  existingTranslations.value = translations;

  // Also update the current SRT list in the store
  if (ttsStore.currentSrtList) {
    const index = ttsStore.srtLists.findIndex(
      (item) => item.name === ttsStore.currentSrtList.name
    );
    if (index !== -1) {
      ttsStore.srtLists[index].items = [...srtItems.value];
    }
  }
};

// Hàm xử lý khi người dùng nhấn nút dịch với SRT Service
const handleTranslateWithSrtServiceTerm = async () => {
  // Xác thực đầu vào và dừng nếu có lỗi
  if (!validateBeforeTranslate()) {
    return;
  }

  // Ensure we have a terminology dictionary
  if (!await ensureTerminologyDictionary()) {
    return;
  }

  // Configure the translate service with the current API key and model
  let apiKey = '';
  const provider = getProviderForModel(ttsStore.model);

  if (provider === 'deepseek') {
    apiKey = ttsStore.deepseekKey;
  } else if (provider === 'gemini') {
    apiKey = ttsStore.geminiKey;
  } else if (provider === 'openai') {
    apiKey = ttsStore.openaiKey;
  }
  console.log("API key provided:", apiKey, ttsStore.model);

  // Configure the translate service
  configureTranslateService(apiKey, ttsStore.model);

  translating.value = true;
  translationProgress.value = 0;
  isPaused.value = false;
  pauseStateRef.value = false;
  translationError.value = null;

  try {
    // Đặt lại trạng thái cho tất cả phụ đề về "translating" khi dịch lại từ đầu
    const resetSubtitles = srtItems.value.map((sub) => ({
      ...sub,
      status: "translating",
      translatedText: "", // Xóa bản dịch cũ
      error: undefined,
    }));
    srtItems.value = resetSubtitles;

    // Chuẩn bị dữ liệu cho translateSrtService
    const subsForTranslation = srtItems.value.map(item => ({
      index: item.id,
      text: item.text,
      start: item.startTime,
      end: item.endTime
    }));

    // Cập nhật tiến độ ban đầu
    translationProgress.value = 5;

    // Gọi dịch vụ dịch SRT với tiến độ
    const translatedTexts = await translateSrtService({
      subs: subsForTranslation,
      batchSize: BATCH_SIZE,
      terminologyDictionary: ttsStore.terminologyDictionary,
      targetLanguage: "Vietnamese", // Hard-coded to Vietnamese as per requirements
      onProgress: (current, total) => {
        translationProgress.value = Math.floor((current / total) * 100);

        // Save progress periodically
        if (current % 5 === 0 || current === total) {
          saveTranslationProgress();
        }
      }
    });

    // Cập nhật kết quả dịch vào srtItems
    const updatedSubtitles = [...srtItems.value];
    translatedTexts.forEach((translatedText, index) => {
      if (index < updatedSubtitles.length) {
        updatedSubtitles[index].translatedText = translatedText;
        updatedSubtitles[index].status = "translated";
      }
    });

    srtItems.value = updatedSubtitles;
    translationProgress.value = 100;

    // Save final translation progress
    saveTranslationProgress();

    message.success("Dịch hoàn tất!");
  } catch (error) {
    console.error("Lỗi khi dịch với SRT Service:", error);
    translationError.value = `Có lỗi xảy ra trong quá trình dịch: ${error.message || "Unknown error"}`;

    // Đánh dấu các phụ đề chưa dịch là lỗi
    const updatedSubtitles = [...srtItems.value];
    updatedSubtitles.forEach(sub => {
      if (sub.status === "translating") {
        sub.status = "error";
        sub.error = "Dịch không thành công";
      }
    });
    srtItems.value = updatedSubtitles;

    // Save partial progress
    saveTranslationProgress();
  } finally {
    translating.value = false;
  }
};

// Function to continue translation from where it left off
const handleContinueTranslation = async () => {
  // Xác thực đầu vào và dừng nếu có lỗi
  if (!validateBeforeTranslate()) {
    return;
  }

  // Ensure we have a terminology dictionary
  if (!await ensureTerminologyDictionary()) {
    return;
  }

  // Configure the translate service with the current API key and model
  let apiKey = '';
  const provider = getProviderForModel(ttsStore.model);

  if (provider === 'deepseek') {
    apiKey = ttsStore.deepseekKey;
  } else if (provider === 'gemini') {
    apiKey = ttsStore.geminiKey;
  } else if (provider === 'openai') {
    apiKey = ttsStore.openaiKey;
  }

  // Configure the translate service
  configureTranslateService(apiKey, ttsStore.model);

  translating.value = true;
  translationProgress.value = 0;
  isPaused.value = false;
  pauseStateRef.value = false;
  translationError.value = null;

  try {
    // Find untranslated subtitles
    const untranslatedSubtitles = [];
    const allSubtitles = [...srtItems.value];

    allSubtitles.forEach((sub, index) => {
      if (sub.status !== "translated" || !sub.translatedText) {
        // Mark as translating
        sub.status = "translating";
        sub.error = undefined;
        untranslatedSubtitles.push({
          index: sub.id,
          text: sub.text,
          start: sub.startTime,
          end: sub.endTime,
          originalIndex: index
        });
      }
    });

    // Update the UI with the updated status
    srtItems.value = allSubtitles;

    if (untranslatedSubtitles.length === 0) {
      message.info("All subtitles are already translated.");
      translating.value = false;
      return;
    }

    // Calculate initial progress based on already translated items
    const initialProgress = Math.floor((srtItems.value.length - untranslatedSubtitles.length) / srtItems.value.length * 100);
    translationProgress.value = initialProgress;

    // Prepare data for translation service
    const subsForTranslation = untranslatedSubtitles.map(item => ({
      index: item.index,
      text: item.text,
      start: item.start,
      end: item.end
    }));

    // Call translation service with progress tracking
    const translatedTexts = await translateSrtService({
      subs: subsForTranslation,
      batchSize: BATCH_SIZE,
      terminologyDictionary: ttsStore.terminologyDictionary,
      targetLanguage: "Vietnamese", // Hard-coded to Vietnamese as per requirements
      onProgress: (current, total) => {
        // Calculate progress considering already translated items
        const progressPercentage = Math.floor(current / total * (100 - initialProgress));
        translationProgress.value = initialProgress + progressPercentage;

        // Save progress periodically
        if (current % 5 === 0 || current === total) {
          saveTranslationProgress();
        }
      }
    });

    // Update translation results
    const updatedSubtitles = [...srtItems.value];
    translatedTexts.forEach((translatedText, index) => {
      const originalIndex = untranslatedSubtitles[index].originalIndex;
      if (originalIndex < updatedSubtitles.length) {
        updatedSubtitles[originalIndex].translatedText = translatedText;
        updatedSubtitles[originalIndex].status = "translated";
      }
    });

    srtItems.value = updatedSubtitles;
    translationProgress.value = 100;

    // Save final translation progress
    saveTranslationProgress();

    message.success("Dịch hoàn tất!");
  } catch (error) {
    console.error("Lỗi khi dịch với SRT Service:", error);
    translationError.value = `Có lỗi xảy ra trong quá trình dịch: ${error.message || "Unknown error"}`;

    // Mark untranslated subtitles as error
    const updatedSubtitles = [...srtItems.value];
    updatedSubtitles.forEach(sub => {
      if (sub.status === "translating") {
        sub.status = "error";
        sub.error = "Dịch không thành công";
      }
    });
    srtItems.value = updatedSubtitles;

    // Save partial progress
    saveTranslationProgress();
  } finally {
    translating.value = false;
  }
};

    const handleSuggestBetterTranslation = async (id, originalText, currentTranslation) => {
        if (!hasApiKey(ttsStore.model)) {
            const provider = getProviderForModel(ttsStore.model);
            // setValidationError(t('errors.apiKeyRequired') + (provider ? ` (${provider})` : ''));
            message.error(t('errors.apiKeyRequired') + (provider ? ` (${provider})` : ''));
            return [];
        }
        let targetLanguage = "Vietnamese";
        try {
            // Xác định ngôn ngữ nguồn dựa trên ngôn ngữ đích
            const sourceLanguage = targetLanguage === "Vietnamese" ? "Chinese" : "Vietnamese";

            // Tạo prompt riêng cho gợi ý bản dịch tốt hơn
            const suggestPrompt = `Hãy đưa ra 3 phiên bản dịch HOÀN TOÀN KHÁC NHAU cho đoạn văn bản sau, mỗi phiên bản với phong cách và cách diễn đạt riêng biệt.

- Văn bản gốc (${sourceLanguage}): "${originalText}"
- Bản dịch hiện tại (${targetLanguage}): "${currentTranslation}"

Yêu cầu cụ thể cho mỗi phiên bản:

1. PHIÊN BẢN THÔNG DỤNG: Ngôn ngữ tự nhiên, dễ hiểu cho số đông người xem. Sử dụng từ ngữ phổ thông, đơn giản mà vẫn diễn đạt đầy đủ ý nghĩa.

2. PHIÊN BẢN HỌC THUẬT: Sát nghĩa với văn bản gốc, sử dụng thuật ngữ chính xác và ngôn ngữ trang trọng. Diễn đạt chặt chẽ về mặt ngữ nghĩa và cú pháp.

3. PHIÊN BẢN SÁNG TẠO: Tự do hơn về mặt diễn đạt, có thể dùng thành ngữ, cách nói địa phương hoặc biểu đạt hiện đại. Truyền tải không chỉ nội dung mà cả cảm xúc và tinh thần của văn bản gốc.

Đảm bảo ba phiên bản phải ĐỦ KHÁC BIỆT để người dùng có những lựa chọn đa dạng. Trả về chính xác 3 phiên bản, mỗi phiên bản trên một dòng, không có đánh số, không có giải thích.`;

            // Đánh dấu đang dịch phụ đề này
            // setCurrentTranslatingItemId(id);


            // Gọi API để lấy gợi ý
            const response = await translateText({
                texts: [originalText],
                targetLanguage,
                prompt: suggestPrompt,
                model: ttsStore.model
            });

            if (response[0]?.error) {
                throw new Error(response[0].error);
            }

            // Parse the response to get suggestions
            let suggestions = [];

            // First try to get the text directly from the response
            if (response[0]?.text) {
                // Split by newlines to get multiple suggestions
                const lines = response[0].text.split('\n').filter(line => line.trim());
                if (lines.length >= 1) {
                    suggestions = lines.slice(0, 3);
                }
            }

            // If we couldn't get suggestions from the first approach, try to use all responses
            if (suggestions.length === 0) {
                for (let i = 0; i < 3 && i < response.length; i++) {
                    if (response[i]?.text) {
                        suggestions.push(response[i].text);
                    }
                }
            }

            // Nếu vẫn không tìm thấy, trả về bản dịch hiện tại
            if (suggestions.length === 0) {
                suggestions.push(currentTranslation);
            }

            // Đảm bảo luôn có đủ 3 phiên bản
            while (suggestions.length < 3) {
                suggestions.push(currentTranslation);
            }

            return suggestions;
        } catch (error) {
            console.error("Error suggesting better translations:", error);
            // setValidationError(t('errors.translationSuggestionFailed'));
            return [currentTranslation];
        } finally {
            // setCurrentTranslatingItemId(null);
        }
    };

// Voice configuration functions
const showVoiceConfigDrawer = () => {
  voiceConfigDrawerVisible.value = true;
};

const closeVoiceConfigDrawer = () => {
  voiceConfigDrawerVisible.value = false;
};

const applyVoiceConfig = (config) => {
  selectedVoiceConfig.value = { ...config };
  message.success(t('voiceConfig.configApplied'));
};

const applyVoiceToAll = (voiceNumber) => {
  // Initialize voices object if it doesn't exist
  if (!selectedVoices.value) {
    selectedVoices.value = {};
  }

  // Check if this voice is already enabled for most subtitles
  const enabledCount = srtItems.value.filter(s =>
    selectedVoices.value[s.id]?.[`voice${voiceNumber}`] === 1
  ).length;

  const shouldEnable = enabledCount < srtItems.value.length / 2;

  // Apply the specified voice to all subtitles
  srtItems.value.forEach(item => {
    if (!selectedVoices.value[item.id]) {
      selectedVoices.value[item.id] = {
        voice1: 0,
        voice2: 0,
        voice3: 0
      };
    }

    if (shouldEnable) {
      // If we're enabling this voice, disable all other voices
      selectedVoices.value[item.id].voice1 = 0;
      selectedVoices.value[item.id].voice2 = 0;
      selectedVoices.value[item.id].voice3 = 0;

      // Enable only the selected voice
      selectedVoices.value[item.id][`voice${voiceNumber}`] = 1;
      item.isVoice = voiceNumber;
    } else {
      // If we're disabling, just disable the selected voice
      selectedVoices.value[item.id][`voice${voiceNumber}`] = 0;
      item.isVoice = 0;
    }
  });

  // Force reactivity update
  selectedVoices.value = { ...selectedVoices.value };

  message.success(shouldEnable ?
    `${t('voiceConfig.voice' + voiceNumber)} ${t('voiceConfig.enabledForAll')}` :
    `${t('voiceConfig.voice' + voiceNumber)} ${t('voiceConfig.disabledForAll')}`
  );
};

const toggleVoiceForSubtitle = (subtitleId, voiceNumber) => {
  // Initialize if not exists
  if (!selectedVoices.value[subtitleId]) {
    selectedVoices.value[subtitleId] = {
      voice1: 0,
      voice2: 0,
      voice3: 0
    };
  }

  // Get current value
  const currentValue = selectedVoices.value[subtitleId][`voice${voiceNumber}`];

  if (currentValue === 0) {
    // If we're enabling this voice, disable all other voices first
    selectedVoices.value[subtitleId].voice1 = 0;
    selectedVoices.value[subtitleId].voice2 = 0;
    selectedVoices.value[subtitleId].voice3 = 0;

    // Then enable the selected voice
    selectedVoices.value[subtitleId][`voice${voiceNumber}`] = 1;
    srtItems.value.find(item => item.id === subtitleId).isVoice = voiceNumber;
  } else {
    // If we're disabling, just disable the selected voice
    selectedVoices.value[subtitleId][`voice${voiceNumber}`] = 0;
    srtItems.value.find(item => item.id === subtitleId).isVoice = 0;
  }

  // Update the selectedVoices ref to trigger reactivity
  selectedVoices.value = { ...selectedVoices.value };
};

const isVoiceSelectedForSubtitle = (subtitleId, voiceNumber) => {
  return srtItems.value.find(item => item.id === subtitleId).isVoice === voiceNumber;
  // return selectedVoices.value[subtitleId]?.[`voice${voiceNumber}`] === 1;
};

// Audio generation functions
const generateAudioForSubtitle = async (subtitle, useTranslatedText = true) => {

  // if (generatingAudio.value) return;

  const text = useTranslatedText ? subtitle.translatedText : subtitle.text;
  if (!text) {
    message.error(useTranslatedText ? 'No translated text available' : 'No text available');
    return;
  }

  // Check if any voice is selected for this subtitle
  if (!subtitle.isVoice || subtitle.isVoice === 0) {
    message.error(t('voiceConfig.selectVoice'));
    return;
  }

  try {
    generatingAudio.value = true;
    state.currentPlayingSubtitleId = subtitle.id;

    // Generate audio for the selected voice
    const voiceNumber = subtitle.isVoice;
    const voiceConfig = selectedVoiceConfig.value[`voice${voiceNumber}`];
    await generateSingleVoiceAudio(subtitle, text, voiceConfig, voiceNumber);

    message.success(t('voiceConfig.audioGenerated'));
  } catch (error) {
    console.error('Error generating audio:', error);
    message.error('Error generating audio: ' + error.message);
  } finally {
    generatingAudio.value = false;
    state.currentPlayingSubtitleId = null;
  }
};

const generateSingleVoiceAudio = async (subtitle, text, voiceConfig, voiceNumber) => {
  if (!voiceConfig.enabled) return;

  const audio_config = {
    speech_rate: voiceConfig.speed,
    volume_gain_db: voiceConfig.volume,
    pitch: voiceConfig.pitch,
    rate: voiceConfig.rate,
    trim: voiceConfig.trim
  };

  try {
    const response = await window.electronAPI.generateTTS({
      text: text,
      speaker: voiceConfig.speaker,
      workspaceId: ttsStore.workspaceId,
      cookie: ttsStore.cookie,
      typeEngine: ttsStore.typeEngine,
      language: selectedVoiceConfig.value.language,
      audio_config
    });

    if (response.success) {
      // Update the subtitle with the audio URL
      const updatedSubtitles = [...srtItems.value];
      const index = updatedSubtitles.findIndex(s => s.id === subtitle.id);

      if (index !== -1) {
        updatedSubtitles[index] = {
          ...updatedSubtitles[index],
          audioUrl: response.audioUrl,
          [`audioUrl${voiceNumber}`]: response.audioUrl,
          [`audioDuration${voiceNumber}`]: response.duration,
          [`isGenerated${voiceNumber}`]: true
        };


        srtItems.value = updatedSubtitles;

        // Add to generated audios in the store
        const newAudio = {
          id: Date.now() + voiceNumber,
          text: text,
          speaker: voiceConfig.speaker,
          url: response.audioUrl,
          duration: response.duration,
          timestamp: new Date().toISOString(),
          voice: ttsStore.speakers.find(s => s.id === voiceConfig.speaker)?.name || '',
          subtitleId: subtitle.id,
          voiceNumber: voiceNumber
        };

        ttsStore.generatedAudios.push(newAudio);
      }
    } else {
      throw new Error(response.message || 'Failed to generate audio');
    }
  } catch (error) {
    console.error(`Error generating audio for voice ${voiceNumber}:`, error);
    throw error;
  }
};

const generateAllAudio = async (useTranslatedText = true) => {
  if (generatingAudio.value) return;

  if (srtItems.value.length === 0) {
    message.error('No subtitles available');
    return;
  }

  try {
    generatingAudio.value = true;
    audioProgress.value = 0;

    // Filter subtitles that have voices selected
    const subtitlesToProcess = srtItems.value.filter(subtitle =>
      subtitle.isVoice && subtitle.isVoice > 0
    );

    if (subtitlesToProcess.length === 0) {
      message.error(t('voiceConfig.selectVoice'));
      generatingAudio.value = false;
      return;
    }
    console.log('subtitlesToProcess', subtitlesToProcess);

    // Process each subtitle
    for (let i = 0; i < subtitlesToProcess.length; i++) {
      const subtitle = subtitlesToProcess[i];
      state.currentPlayingSubtitleId = subtitle.id;
      if (!subtitle.isEnabled) continue;
      await generateAudioForSubtitle(subtitle, useTranslatedText);

      // Update progress
      audioProgress.value = Math.floor(((i + 1) / subtitlesToProcess.length) * 100);
    }

    message.success(t('voiceConfig.audioGenerated'));
  } catch (error) {
    console.error('Error generating audio for all subtitles:', error);
    message.error('Error generating audio: ' + error.message);
  } finally {
    generatingAudio.value = false;
    audioProgress.value = 100;
    state.currentPlayingSubtitleId = null;
  }
};

async function generateAllAudioIsNotGenerated(useTranslatedText = true) {
  if (generatingAudio.value) return;

  if (srtItems.value.length === 0) {
    message.error('No subtitles available');
    return;
  }

  try {
    generatingAudio.value = true;
    audioProgress.value = 0;

    // Filter subtitles that have voices selected
    const subtitlesToProcess = srtItems.value.filter(subtitle =>
      subtitle.isVoice && subtitle.isVoice > 0 && !subtitle[`isGenerated${subtitle.isVoice}`]
    );

    if (subtitlesToProcess.length === 0) {
      message.error(t('voiceConfig.nothingToGenerate'));
      generatingAudio.value = false;
      return;
    }

    // Process each subtitle
    for (let i = 0; i < subtitlesToProcess.length; i++) {
      const subtitle = subtitlesToProcess[i];
      state.currentPlayingSubtitleId = subtitle.id;
      if (!subtitle.isEnabled) continue;
      await generateAudioForSubtitle(subtitle, useTranslatedText);

      // Update progress
      audioProgress.value = Math.floor(((i + 1) / subtitlesToProcess.length) * 100);
    }

    message.success(t('voiceConfig.audioGenerated'));
  } catch (error) {
    console.error('Error generating audio for all subtitles:', error);
    message.error('Error generating audio: ' + error.message);
  } finally {
    generatingAudio.value = false;
    audioProgress.value = 100;
    state.currentPlayingSubtitleId = null;
  }
}










async function joinMp3FilesV2() {
  if (joiningAudio.value) return;

  // Check if there are any generated audio files
  const hasGeneratedAudio = srtItems.value.some(item => {
    const voiceNumber = item.isVoice;
    return voiceNumber > 0 && item[`isGenerated${voiceNumber}`];
  });

  if (!hasGeneratedAudio) {
    message.error(t('voiceConfig.noAudioToJoin'));
    return;
  }

  joiningAudio.value = true;

  try {
    // Create a temporary directory to store the files
    // const tempDir = 'temp_audio';
    const tempDir =(await electronAPI.getCurrentDir()).currentDir;

    // Set audioUrl based on isVoice number
    srtItems.value.forEach(item => {
      const voiceNumber = item.isVoice;
      if (voiceNumber > 0 && item[`isGenerated${voiceNumber}`]) {
        item.audioUrl = item[`audioUrl${voiceNumber}`];
        item.isGenerated = true;
        item.trim = selectedVoiceConfig.value[`voice${voiceNumber}`].trim;
      }
    });

    // Save all audio files to the temp directory
    const fileList = [];
    const validItems = srtItems.value.filter(item => item.isGenerated && item.audioUrl);

    for (let i = 0; i < validItems.length; i++) {
      const item = validItems[i];
      const fileName = `segment-${item.index}.mp3`;
      const filePath = `${tempDir}\\${fileName}`;
      const protocol = item.audioUrl.startsWith('file://')

      if(!protocol){
        // Download the audio file
        await electronAPI.downloadFile(item.audioUrl, filePath);
        fileList.push(filePath);
      } else{
        fileList.push(item.audioUrl.replace('file://', ''));
      }

    }

    // Create a file list for FFmpeg
    // const fileListPath = `${tempDir}/filelist.txt`;
    // const fileListContent = fileList.map(file => `file '${file}'`).join('\n');
    // await electronAPI.writeFile(fileListPath, fileListContent);


    // Run FFmpeg to join the files
    const outputPath = `${tempDir}\\joined_audio_${Date.now()}.mp3`;
    const result = await electronAPI.mergeAudioFiles(fileList, outputPath,JSON.parse(JSON.stringify(srtItems.value)));

    if (result.success) {
      // Provide download link for the combined file
      message.success('MP3 files combined successfully');

      // Open the file in the default player
      await electronAPI.openFile(outputPath);
    } else {
      console.error('Error combining MP3 files:', result);

      // Display a more detailed error message
      let errorMessage = 'Error combining MP3 files';
      if (result.error) {
        errorMessage += ': ' + result.error;
      }
      if (result.details) {
        errorMessage += '\nDetails: ' + result.details;
      }
      if (result.stack) {
        console.error('Error stack:', result.stack);
      }

      // Show error message with more details
      message.error(errorMessage);
    }
  } catch (error) {
    console.error('Error joining MP3 files:', error);
    message.error('Error joining MP3 files: ' + error.message);
  } finally {
    joiningAudio.value = false;
  }
}

// function handleVideoTimeUpdate(time) {
//   // playCurrentTime.value = time;
//   const currentSubtitle = srtItems.value.find(item => item.startTime <= time && item.endTime >= time);
//   if (currentSubtitle) {
//     state.currentPlayingSubtitleId = currentSubtitle.id;
//   } else {
//     state.currentPlayingSubtitleId = null;
//   }
// }


function handlePlayVideo(record) {
  state.currentPlayingSubtitleId = record.id;
  if (state.videoPlayer) {
    state.videoPlayer.$refs.video.currentTime = record.startTime
    // play from start time
    state.videoPlayer.$refs.video.play();
    record.isPlayable = true;
    // pause is end time
    setTimeout(() => {
      state.videoPlayer.$refs.video.pause();
      record.isPlayable = false;
    }, (record.endTime - record.startTime) * 1200);
  }
}


function toggleAllEnabled() {
  allEnabled.value = !allEnabled.value;
  indeterminate.value = false;
  srtItems.value.forEach(item => {
    item.isEnabled = allEnabled.value;
  });
}

function checkIndeterminate() {
  const enabledCount = srtItems.value.filter(item => item.isEnabled).length;
  indeterminate.value = enabledCount > 0 && enabledCount < srtItems.value.length;
}
function formatTimestamp(timeInSeconds) {
  const hours = Math.floor(timeInSeconds / 3600);
  const minutes = Math.floor((timeInSeconds % 3600) / 60);
  const seconds = Math.floor(timeInSeconds % 60);
  const centiseconds = Math.floor((timeInSeconds - Math.floor(timeInSeconds)) * 100);
  return `${hours}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}.${String(centiseconds).padStart(2, '0')}`;
}
async function exportSrt() {
  const content = srtItems.value.map(item => {
    return `${item.index}\n${formatTimestamp(item.startTime)} --> ${formatTimestamp(item.endTime)}\n${item.translatedText || item.text}\n\n`;
  }).join('');

  const filePath = ttsStore.currentSrtList.path.replace('.srt', '_exported.srt');
  await electronAPI.saveFile(filePath, content);
  message.success('Exported SRT file');
}





</script>

<style scoped>
/* Custom scrollbar styling để match với dark theme */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #6B7280;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
</style>
