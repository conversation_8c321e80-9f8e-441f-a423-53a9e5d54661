const ffmpeg = require('fluent-ffmpeg');
const path = require('path');

async function trimAudio (inputFile, duration, outputFile) {
  return new Promise((resolve, reject) => {
    ffmpeg(inputFile)
      .setStartTime(0)
      .setDuration(duration)
      .output(outputFile)
      .on('end', () => {
        console.log('Audio trimmed successfully');
        resolve(outputFile);
      })
      .on('error', (err) => {
        console.error('Error trimming audio:', err);
        reject(err);
      })
      .run();
  });
}
async function getAudioDuration (audioPath) {
  try {
    const audioInfo = await new Promise((resolve, reject) => {
      ffmpeg.ffprobe(audioPath, function (err, metadata) {
        if (err) {
          reject(err);
        } else {
          resolve(metadata);
        }
      });
    });
    const duration = parseFloat(audioInfo.format.duration);
    return duration;

  } catch (error) {
    console.error("Error fetching audio duration:", error);
    throw error;
  }
}
const getVideoInfo = async (event, filePath) => {
  if (!filePath) {
    throw new Error("No input specified");
  }

  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        reject(new Error(`FFprobe error: ${err.message}`));
        return;
      }

      if (!metadata || !metadata.streams) {
        reject(new Error("Invalid video file or no metadata found"));
        return;
      }

      const videoStream = metadata.streams.find(
        (stream) => stream.codec_type === "video"
      );
      if (!videoStream) {
        reject(new Error("No video stream found in file"));
        return;
      }

      // Parse frame rate
      let fps = 0;
      if (videoStream.r_frame_rate) {
        const [num, den] = videoStream.r_frame_rate.split("/");
        fps = parseFloat(num) / parseFloat(den);
      }

      // Calculate total frames
      let totalFrames = parseInt(videoStream.nb_frames) || 0;
      if (!totalFrames && metadata.format.duration) {
        totalFrames = Math.ceil(fps * metadata.format.duration);
      }

      resolve({
        width: videoStream.width || null,
        height: videoStream.height || null,
        fps: fps,
        duration: metadata.format.duration || null,
        total_frames: totalFrames
      });
    });
  });
};
const getEncoders = () => {
  return new Promise((resolve, reject) => {
    ffmpeg.getAvailableEncoders((err, encoders) => {
      if (err) reject(err);
      else resolve(encoders);
    });
  });
};

let encoderVal = null

async function getEncoder() {
  if (encoderVal) return encoderVal;
  try {
    const encoder = await getEncoders();
    if (encoder["h264_nvenc"]) return "h264_nvenc";
    if (encoder["h264_amf"]) return "h264_amf";
    return  (encoderVal = "libx264");
  } catch (error) {
    return  (encoderVal = "libx264");
  }
}


async function getFrameVideo(event, videoPath, second, outputPath, cropData = null) {
  return new Promise((resolve, reject) => {
    const command = ffmpeg(videoPath)
      .seekInput(second)
      .outputOptions('-frames:v 1');

    // Nếu có cropData, áp dụng filter crop
    if (cropData && cropData.length === 4) {
      const [x1, y1, x2, y2] = cropData;
      command.ffprobe((err, metadata) => {
        if (err) return reject(err);

        const width = metadata.streams[0].width;
        const height = metadata.streams[0].height;

        const cropWidth = Math.floor((x2 - x1) * width);
        const cropHeight = Math.floor((y2 - y1) * height);
        const cropX = Math.floor(x1 * width);
        const cropY = Math.floor(y1 * height);

        const cropFilter = `crop=${cropWidth}:${cropHeight}:${cropX}:${cropY}`;
        command.videoFilters(cropFilter);
        command
          .output(outputPath)
          .on('end', () => {
            console.log(`✅ Frame at ${second}s saved to ${outputPath}`);
            resolve(outputPath);
          })
          .on('error', (err) => {
            console.error('❌ Error extracting frame:', err.message);
            reject(err);
          })
          .run();
      });
    } else {
      // Nếu không crop
      command
        .output(outputPath)
        .on('end', () => {
          console.log(`✅ Frame at ${second}s saved to ${outputPath}`);
          resolve(outputPath);
        })
        .on('error', (err) => {
          console.error('❌ Error extracting frame:', err.message);
          reject(err);
        })
        .run();
    }
  });
}











module.exports = {
    trimAudio,
    getAudioDuration,
    getVideoInfo,
    getEncoder,
    getFrameVideo
}