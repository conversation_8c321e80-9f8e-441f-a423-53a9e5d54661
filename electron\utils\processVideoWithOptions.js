const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');
const { processVideoSimplified } = require('./videoRendererSimplified');
const { getVideoInfo, getEncoder } = require('../ffmpegHandler');

// example
const _renderConfig = {
  srtItems: [
    {
      index: 1,
      id: 1,
      text: '都说动画界有四大挂臂',
      startTime: 0.02,
      endTime: 1.56,
      start: '00:00:00,020',
      end: '00:00:01,560',
      translatedText: 'Người ta thường nói giới hoạt hình có Tứ Đại Quải Tý',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nguoi-ta-thuong-noi-gioi1748136742869.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nguoi-ta-thuong-noi-gioi1748136742869.mp3',
      audioDuration1: 2520,
      isGenerated1: true,
    },
    {
      index: 2,
      id: 2,
      text: '但能稳占据天花板的非德鲁比莫属',
      startTime: 1.56,
      endTime: 3.8,
      start: '00:00:01,560',
      end: '00:00:03,800',
      translatedText: 'Nhưng vị trí đỉnh cao bất khả xâm phạm chắc chắn thuộc về Droopy',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-vi-tri-dinh-cao-ba1748136744412.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-vi-tri-dinh-cao-ba1748136744412.mp3',
      audioDuration1: 3288,
      isGenerated1: true,
    },
    {
      index: 3,
      id: 3,
      text: '只要他那神秘的被动技能一经触发',
      startTime: 3.8,
      endTime: 5.82,
      start: '00:00:03,800',
      end: '00:00:05,820',
      translatedText: 'Chỉ cần kỹ năng bị động thần bí của hắn được kích hoạt',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chi-can-ky-nang-bi-dong-1748136746349.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chi-can-ky-nang-bi-dong-1748136746349.mp3',
      audioDuration1: 2568,
      isGenerated1: true,
    },
    {
      index: 4,
      id: 4,
      text: '那场面之震撼',
      startTime: 5.82,
      endTime: 7,
      start: '00:00:05,820',
      end: '00:00:07,000',
      translatedText: 'Cảnh tượng kinh hoàng ấy',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\canh-tuong-kinh-hoang-ay1748136747399.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\canh-tuong-kinh-hoang-ay1748136747399.mp3',
      audioDuration1: 1345,
      isGenerated1: true,
    },
    {
      index: 5,
      id: 5,
      text: '简直让人不敢细想',
      startTime: 7,
      endTime: 8.24,
      start: '00:00:07,000',
      end: '00:00:08,240',
      translatedText: 'Khiến người ta không dám nghĩ sâu thêm',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khien-nguoi-ta-khong-dam1748136748359.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khien-nguoi-ta-khong-dam1748136748359.mp3',
      audioDuration1: 2045,
      isGenerated1: true,
    },
    {
      index: 6,
      id: 6,
      text: '而能与这位传奇角色相提并论的',
      startTime: 8.24,
      endTime: 10.34,
      start: '00:00:08,240',
      end: '00:00:10,340',
      translatedText: 'Nhân vật huyền thoại duy nhất có thể sánh ngang với hắn',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhan-vat-huyen-thoai-duy1748136749316.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhan-vat-huyen-thoai-duy1748136749316.mp3',
      audioDuration1: 2520,
      isGenerated1: true,
    },
    {
      index: 7,
      id: 7,
      text: '便是这只举止优雅',
      startTime: 10.34,
      endTime: 11.58,
      start: '00:00:10,340',
      end: '00:00:11,580',
      translatedText: 'Chính là con cáo yểu điệu này',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chinh-la-con-cao-yeu-die1748136750559.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chinh-la-con-cao-yeu-die1748136750559.mp3',
      audioDuration1: 1680,
      isGenerated1: true,
    },
    {
      index: 8,
      id: 8,
      text: '且智谋超群的狐狸',
      startTime: 11.58,
      endTime: 13.04,
      start: '00:00:11,580',
      end: '00:00:13,040',
      translatedText: 'Với trí mưu siêu quần bạt chúng',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\voi-tri-muu-sieu-quan-ba1748136751475.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\voi-tri-muu-sieu-quan-ba1748136751475.mp3',
      audioDuration1: 1896,
      isGenerated1: true,
    },
    {
      index: 9,
      id: 9,
      text: '龙场主对这只狐狸可谓是恨到了极点',
      startTime: 13.04,
      endTime: 39.6,
      start: '00:00:13,040',
      end: '00:00:39,600',
      translatedText: 'Chủ trại Long căm ghét con cáo này đến tận xương tủy',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chu-trai-long-cam-ghet-c1748136752497.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chu-trai-long-cam-ghet-c1748136752497.mp3',
      audioDuration1: 2655,
      isGenerated1: true,
    },
    {
      index: 10,
      id: 10,
      text: '为了将其成功擒获',
      startTime: 39.6,
      endTime: 40.84,
      start: '00:00:39,600',
      end: '00:00:40,840',
      translatedText: 'Để bắt sống được nó',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\de-bat-song-duoc-no1748136753692.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\de-bat-song-duoc-no1748136753692.mp3',
      audioDuration1: 1272,
      isGenerated1: true,
    },
    {
      index: 11,
      id: 11,
      text: '他不惜赤重金',
      startTime: 40.84,
      endTime: 41.9,
      start: '00:00:40,840',
      end: '00:00:41,900',
      translatedText: 'Hắn không ngại bỏ ra một khoản tiền lớn',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\han-khong-ngai-bo-ra-mot1748136754478.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\han-khong-ngai-bo-ra-mot1748136754478.mp3',
      audioDuration1: 2005,
      isGenerated1: true,
    },
    {
      index: 12,
      id: 12,
      text: '招募了一只由精英猎犬组成的捕狐小队',
      startTime: 41.9,
      endTime: 44.26,
      start: '00:00:41,900',
      end: '00:00:44,260',
      translatedText: 'Tuyển mộ một đội bắt cáo gồm toàn chó săn tinh nhuệ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\tuyen-mo-mot-doi-bat-cao1748136755479.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\tuyen-mo-mot-doi-bat-cao1748136755479.mp3',
      audioDuration1: 2525,
      isGenerated1: true,
    },
    {
      index: 13,
      id: 13,
      text: '身为小队一员的德鲁比',
      startTime: 44.26,
      endTime: 45.72,
      start: '00:00:44,260',
      end: '00:00:45,720',
      translatedText: 'Là một thành viên trong đội, Droopy',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\la-mot-thanh-vien-trong-1748136756665.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\la-mot-thanh-vien-trong-1748136756665.mp3',
      audioDuration1: 2272.6530000000002,
      isGenerated1: true,
    },
    {
      index: 14,
      id: 14,
      text: '也迎来了证明自身实力的绝佳时机',
      startTime: 45.72,
      endTime: 47.9,
      start: '00:00:45,720',
      end: '00:00:47,900',
      translatedText: 'Cũng có cơ hội vàng để chứng minh năng lực bản thân',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\cung-co-co-hoi-vang-de-c1748136757922.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\cung-co-co-hoi-vang-de-c1748136757922.mp3',
      audioDuration1: 2664,
      isGenerated1: true,
    },
    {
      index: 15,
      id: 15,
      text: '为了充分激发猎犬们的昂扬斗志',
      startTime: 47.9,
      endTime: 49.86,
      start: '00:00:47,900',
      end: '00:00:49,860',
      translatedText: 'Để khơi dậy tinh thần chiến đấu cao độ của bầy chó săn',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\de-khoi-day-tinh-than-ch1748136759461.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\de-khoi-day-tinh-than-ch1748136759461.mp3',
      audioDuration1: 2705,
      isGenerated1: true,
    },
    {
      index: 16,
      id: 16,
      text: '龙场主特意拿出香气扑鼻的战斧牛排',
      startTime: 49.86,
      endTime: 52.46,
      start: '00:00:49,860',
      end: '00:00:52,460',
      translatedText: 'Chủ trại Long đặc biệt mang ra món bò bít tết chiến rìu thơm phức',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chu-trai-long-dac-biet-m1748136760624.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chu-trai-long-dac-biet-m1748136760624.mp3',
      audioDuration1: 3205,
      isGenerated1: true,
    },
    {
      index: 17,
      id: 17,
      text: '作为丰厚诱人的奖励',
      startTime: 52.46,
      endTime: 53.8,
      start: '00:00:52,460',
      end: '00:00:53,800',
      translatedText: 'Làm phần thưởng hậu hĩnh đầy hấp dẫn',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\lam-phan-thuong-hau-hinh1748136761772.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\lam-phan-thuong-hau-hinh1748136761772.mp3',
      audioDuration1: 1865,
      isGenerated1: true,
    },
    {
      index: 18,
      id: 18,
      text: '在这美食的强大诱惑下',
      startTime: 53.8,
      endTime: 55.34,
      start: '00:00:53,800',
      end: '00:00:55,340',
      translatedText: 'Dưới sức cám dỗ mãnh liệt của món ăn ngon',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\duoi-suc-cam-do-manh-lie1748136762762.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\duoi-suc-cam-do-manh-lie1748136762762.mp3',
      audioDuration1: 2496,
      isGenerated1: true,
    },
    {
      index: 19,
      id: 19,
      text: '其他猎犬们如同离弦之剑',
      startTime: 55.34,
      endTime: 57.14,
      start: '00:00:55,340',
      end: '00:00:57,140',
      translatedText: 'Những con chó săn khác lao đi như tên bắn',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-con-cho-san-khac-l1748136763821.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-con-cho-san-khac-l1748136763821.mp3',
      audioDuration1: 2275,
      isGenerated1: true,
    },
    {
      index: 20,
      id: 20,
      text: '迫不及待地夺门而出',
      startTime: 57.14,
      endTime: 58.48,
      start: '00:00:57,140',
      end: '00:00:58,480',
      translatedText: 'Nóng lòng chạy xộc ra khỏi cửa',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nong-long-chay-xoc-ra-kh1748136764851.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nong-long-chay-xoc-ra-kh1748136764851.mp3',
      audioDuration1: 1848,
      isGenerated1: true,
    },
    {
      index: 21,
      id: 21,
      text: '唯有德鲁比依旧保持着一贯的沉稳冷静',
      startTime: 58.48,
      endTime: 61.2,
      start: '00:00:58,480',
      end: '00:01:01,200',
      translatedText: 'Duy chỉ có Droopy vẫn giữ được vẻ điềm tĩnh vốn có',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\duy-chi-co-droopy-van-gi1748136765789.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\duy-chi-co-droopy-van-gi1748136765789.mp3',
      audioDuration1: 3048,
      isGenerated1: true,
    },
    {
      index: 22,
      id: 22,
      text: '因为他心里清楚',
      startTime: 61.2,
      endTime: 62.1,
      start: '00:01:01,200',
      end: '00:01:02,100',
      translatedText: 'Bởi trong lòng chú biết rõ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\boi-trong-long-chu-biet-1748136766872.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\boi-trong-long-chu-biet-1748136766872.mp3',
      audioDuration1: 1536,
      isGenerated1: true,
    },
    {
      index: 23,
      id: 23,
      text: '这只狐狸绝非泛泛之辈',
      startTime: 62.1,
      endTime: 63.74,
      start: '00:01:02,100',
      end: '00:01:03,740',
      translatedText: 'Con cáo này tuyệt đối không phải hạng tầm thường',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-cao-nay-tuyet-doi-kh1748136767864.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-cao-nay-tuyet-doi-kh1748136767864.mp3',
      audioDuration1: 2280,
      isGenerated1: true,
    },
    {
      index: 24,
      id: 24,
      text: '不可掉以轻心',
      startTime: 63.74,
      endTime: 64.76,
      start: '00:01:03,740',
      end: '00:01:04,760',
      translatedText: 'Không thể lơ là cảnh giác',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khong-the-lo-la-canh-gia1748136768878.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khong-the-lo-la-canh-gia1748136768878.mp3',
      audioDuration1: 1365,
      isGenerated1: true,
    },
    {
      index: 25,
      id: 25,
      text: '事实正如德鲁比所料',
      startTime: 64.76,
      endTime: 66.4,
      start: '00:01:04,760',
      end: '00:01:06,400',
      translatedText: 'Sự thật đúng như Droopy dự đoán',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\su-that-dung-nhu-droopy-1748136769638.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\su-that-dung-nhu-droopy-1748136769638.mp3',
      audioDuration1: 2208,
      isGenerated1: true,
    },
    {
      index: 26,
      id: 26,
      text: '面对即将来临的危机',
      startTime: 66.4,
      endTime: 67.8,
      start: '00:01:06,400',
      end: '00:01:07,800',
      translatedText: 'Đối mặt với nguy cơ sắp ập tới',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\doi-mat-voi-nguy-co-sap-1748136770551.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\doi-mat-voi-nguy-co-sap-1748136770551.mp3',
      audioDuration1: 1968,
      isGenerated1: true,
    },
    {
      index: 27,
      id: 27,
      text: '狐狸不仅没有慌乱',
      startTime: 67.8,
      endTime: 68.92,
      start: '00:01:07,800',
      end: '00:01:08,920',
      translatedText: 'Con cáo không những không hoảng loạn',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-cao-khong-nhung-khon1748136771512.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-cao-khong-nhung-khon1748136771512.mp3',
      audioDuration1: 1656,
      isGenerated1: true,
    },
    {
      index: 28,
      id: 28,
      text: '反而凭借着敏捷矫健的身手',
      startTime: 68.92,
      endTime: 70.62,
      start: '00:01:08,920',
      end: '00:01:10,620',
      translatedText: 'Ngược lại còn dựa vào thân thủ nhanh nhẹn điêu luyện',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nguoc-lai-con-dua-vao-th1748136772382.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nguoc-lai-con-dua-vao-th1748136772382.mp3',
      audioDuration1: 2375,
      isGenerated1: true,
    },
    {
      index: 29,
      id: 29,
      text: '将前来围捕的猎犬彻底碾压',
      startTime: 70.62,
      endTime: 72.6,
      start: '00:01:10,620',
      end: '00:01:12,600',
      translatedText: 'Áp đảo hoàn toàn lũ chó săn đến vây bắt',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ap-dao-hoan-toan-lu-cho-1748136773481.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ap-dao-hoan-toan-lu-cho-1748136773481.mp3',
      audioDuration1: 2256,
      isGenerated1: true,
    },
    {
      index: 30,
      id: 30,
      text: '淋漓尽致地展现出强者的风范',
      startTime: 72.6,
      endTime: 74.68,
      start: '00:01:12,600',
      end: '00:01:14,680',
      translatedText: 'Thể hiện đầy đủ phong thái của kẻ mạnh',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\the-hien-day-du-phong-th1748136774483.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\the-hien-day-du-phong-th1748136774483.mp3',
      audioDuration1: 1945,
      isGenerated1: true,
    },
    {
      index: 31,
      id: 31,
      text: '当一阵急促的敲门声响起',
      startTime: 74.68,
      endTime: 76.4,
      start: '00:01:14,680',
      end: '00:01:16,400',
      translatedText: 'Khi một hồi gõ cửa gấp gáp vang lên  ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khi-mot-hoi-go-cua-gap-g1748136775376.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khi-mot-hoi-go-cua-gap-g1748136775376.mp3',
      audioDuration1: 2328,
      isGenerated1: true,
    },
    {
      index: 32,
      id: 32,
      text: '狐狸打开门',
      startTime: 76.4,
      endTime: 77.16,
      start: '00:01:16,400',
      end: '00:01:17,160',
      translatedText: 'Con cáo mở cửa  ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-cao-mo-cua1748136776441.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-cao-mo-cua1748136776441.mp3',
      audioDuration1: 1200,
      isGenerated1: true,
    },
    {
      index: 33,
      id: 33,
      text: '发现竟是动画界有一席之地的德鲁比',
      startTime: 77.16,
      endTime: 79.3,
      start: '00:01:17,160',
      end: '00:01:19,300',
      translatedText: 'Phát hiện ra kẻ đứng trước mặt chính là Droopy - nhân vật có chỗ đứng trong giới hoạt hình  ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\phat-hien-ra-ke-dung-tru1748136777211.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\phat-hien-ra-ke-dung-tru1748136777211.mp3',
      audioDuration1: 4225,
      isGenerated1: true,
    },
    {
      index: 34,
      id: 34,
      text: '面对这位拥有恐怖被动技能的强大对手',
      startTime: 79.3,
      endTime: 81.76,
      start: '00:01:19,300',
      end: '00:01:21,760',
      translatedText: 'Đối mặt với đối thủ mạnh mẽ sở hữu kỹ năng bị động đáng sợ này  ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\doi-mat-voi-doi-thu-manh1748136778537.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\doi-mat-voi-doi-thu-manh1748136778537.mp3',
      audioDuration1: 3432,
      isGenerated1: true,
    },
    {
      index: 35,
      id: 35,
      text: '狡猾的狐狸并未贸然行动',
      startTime: 81.76,
      endTime: 83.44,
      start: '00:01:21,760',
      end: '00:01:23,440',
      translatedText: 'Con cáo xảo quyệt không hành động hấp tấp  ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-cao-xao-quyet-khong-1748136779786.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-cao-xao-quyet-khong-1748136779786.mp3',
      audioDuration1: 1895,
      isGenerated1: true,
    },
    {
      index: 36,
      id: 36,
      text: '她眼珠快速转动',
      startTime: 83.44,
      endTime: 84.72,
      start: '00:01:23,440',
      end: '00:01:24,720',
      translatedText: 'Đôi mắt nó nhanh chóng đảo quanh  ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\doi-mat-no-nhanh-chong-d1748136780647.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\doi-mat-no-nhanh-chong-d1748136780647.mp3',
      audioDuration1: 1775,
      isGenerated1: true,
    },
    {
      index: 37,
      id: 37,
      text: '瞬间记上心来',
      startTime: 84.72,
      endTime: 86.02,
      start: '00:01:24,720',
      end: '00:01:26,020',
      translatedText: 'Ngay lập tức nảy ra kế hoạch  ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ngay-lap-tuc-nay-ra-ke-h1748136781569.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ngay-lap-tuc-nay-ra-ke-h1748136781569.mp3',
      audioDuration1: 1608,
      isGenerated1: true,
    },
    {
      index: 38,
      id: 38,
      text: '只见她将家中那只装进袋子的笨狗',
      startTime: 86.02,
      endTime: 88.04,
      start: '00:01:26,020',
      end: '00:01:28,040',
      translatedText: 'Chỉ thấy nó lôi con chó ngốc nghếch đang bị nhét trong bao ở nhà  ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chi-thay-no-loi-con-cho-1748136782548.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chi-thay-no-loi-con-cho-1748136782548.mp3',
      audioDuration1: 2965,
      isGenerated1: true,
    },
    {
      index: 39,
      id: 39,
      text: '与自己做了个互换',
      startTime: 88.04,
      endTime: 89.1,
      start: '00:01:28,040',
      end: '00:01:29,100',
      translatedText: 'Hoán đổi với chính mình  ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\hoan-doi-voi-chinh-minh1748136783767.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\hoan-doi-voi-chinh-minh1748136783767.mp3',
      audioDuration1: 1368,
      isGenerated1: true,
    },
    {
      index: 40,
      id: 40,
      text: '当做捕获的战利品',
      startTime: 89.1,
      endTime: 90.28,
      start: '00:01:29,100',
      end: '00:01:30,280',
      translatedText: 'Giả vờ đó là chiến lợi phẩm bắt được',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\gia-vo-do-la-chien-loi-p1748136784555.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\gia-vo-do-la-chien-loi-p1748136784555.mp3',
      audioDuration1: 2088,
      isGenerated1: true,
    },
    {
      index: 41,
      id: 41,
      text: '满脸堆笑地递给了德鲁比',
      startTime: 90.28,
      endTime: 91.88,
      start: '00:01:30,280',
      end: '00:01:31,880',
      translatedText: 'Với nụ cười tươi rói, hắn đưa cho Droopy',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\voi-nu-cuoi-tuoi-roi-han1748136785574.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\voi-nu-cuoi-tuoi-roi-han1748136785574.mp3',
      audioDuration1: 2795.102,
      isGenerated1: true,
    },
    {
      index: 42,
      id: 42,
      text: '毫不知情的德鲁比',
      startTime: 91.88,
      endTime: 92.92,
      start: '00:01:31,880',
      end: '00:01:32,920',
      translatedText: 'Droopy hoàn toàn không hay biết gì',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\droopy-hoan-toan-khong-h1748136786904.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\droopy-hoan-toan-khong-h1748136786904.mp3',
      audioDuration1: 2184,
      isGenerated1: true,
    },
    {
      index: 43,
      id: 43,
      text: '一路上哼着欢快的小曲前去领赏',
      startTime: 92.92,
      endTime: 94.88,
      start: '00:01:32,920',
      end: '00:01:34,880',
      translatedText: 'Vừa đi vừa nghêu ngao bài hát vui vẻ trên đường đi nhận thưởng',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\vua-di-vua-ngheu-ngao-ba1748136787832.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\vua-di-vua-ngheu-ngao-ba1748136787832.mp3',
      audioDuration1: 3072,
      isGenerated1: true,
    },
    {
      index: 44,
      id: 44,
      text: '还得意洋洋地向同伴炫耀',
      startTime: 94.88,
      endTime: 96.44,
      start: '00:01:34,880',
      end: '00:01:36,440',
      translatedText: 'Còn đắc ý khoe khoang với đồng đội',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-dac-y-khoe-khoang-vo1748136788915.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-dac-y-khoe-khoang-vo1748136788915.mp3',
      audioDuration1: 1896,
      isGenerated1: true,
    },
    {
      index: 45,
      id: 45,
      text: '然而这一炫耀竟被二狗捷足先登',
      startTime: 96.44,
      endTime: 98.82,
      start: '00:01:36,440',
      end: '00:01:38,820',
      translatedText: 'Nhưng không ngờ sự khoe khoang này lại bị Nhị Cẩu cướp mất công đầu',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-khong-ngo-su-khoe-1748136789803.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-khong-ngo-su-khoe-1748136789803.mp3',
      audioDuration1: 3485,
      isGenerated1: true,
    },
    {
      index: 46,
      id: 46,
      text: '而生性暴躁的二狗',
      startTime: 98.82,
      endTime: 99.98,
      start: '00:01:38,820',
      end: '00:01:39,980',
      translatedText: 'Vốn tính nóng nảy, Nhị Cẩu',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\von-tinh-nong-nay-nhi-ca1748136790955.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\von-tinh-nong-nay-nhi-ca1748136790955.mp3',
      audioDuration1: 1959.184,
      isGenerated1: true,
    },
    {
      index: 47,
      id: 47,
      text: '感觉麻袋里的家伙不安分',
      startTime: 99.98,
      endTime: 101.4,
      start: '00:01:39,980',
      end: '00:01:41,400',
      translatedText: 'Cảm thấy kẻ trong bao tải không yên phận',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\cam-thay-ke-trong-bao-ta1748136792179.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\cam-thay-ke-trong-bao-ta1748136792179.mp3',
      audioDuration1: 2112,
      isGenerated1: true,
    },
    {
      index: 48,
      id: 48,
      text: '于是抄起铁锹便一顿猛砸',
      startTime: 101.4,
      endTime: 103.2,
      start: '00:01:41,400',
      end: '00:01:43,200',
      translatedText: 'Bèn vác xẻng lên đập túi bụi',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ben-vac-xeng-len-dap-tui1748136793210.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ben-vac-xeng-len-dap-tui1748136793210.mp3',
      audioDuration1: 1605,
      isGenerated1: true,
    },
    {
      index: 49,
      id: 49,
      text: '可他万万没有想到',
      startTime: 103.2,
      endTime: 104.38,
      start: '00:01:43,200',
      end: '00:01:44,380',
      translatedText: 'Nhưng hắn không thể ngờ rằng',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-han-khong-the-ngo-1748136794096.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-han-khong-the-ngo-1748136794096.mp3',
      audioDuration1: 1512,
      isGenerated1: true,
    },
    {
      index: 50,
      id: 50,
      text: '袋子里装的竟是自己平日里',
      startTime: 104.38,
      endTime: 105.96,
      start: '00:01:44,380',
      end: '00:01:45,960',
      translatedText: 'Trong bao kia chính là người thường ngày hắn...',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\trong-bao-kia-chinh-la-n1748136795013.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\trong-bao-kia-chinh-la-n1748136795013.mp3',
      audioDuration1: 2184,
      isGenerated1: true,
    },
    {
      index: 51,
      id: 51,
      text: '最为敬重的老大哥',
      startTime: 105.96,
      endTime: 106.94,
      start: '00:01:45,960',
      end: '00:01:46,940',
      translatedText: 'Người anh cả đáng kính nhất',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nguoi-anh-ca-dang-kinh-n1748136796094.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nguoi-anh-ca-dang-kinh-n1748136796094.mp3',
      audioDuration1: 1296,
      isGenerated1: true,
    },
    {
      index: 52,
      id: 52,
      text: '二狗见状',
      startTime: 106.94,
      endTime: 107.78,
      start: '00:01:46,940',
      end: '00:01:47,780',
      translatedText: 'Nhị Cẩu thấy vậy',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhi-cau-thay-vay1748136797017.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhi-cau-thay-vay1748136797017.mp3',
      audioDuration1: 1004.9999999999999,
      isGenerated1: true,
    },
    {
      index: 53,
      id: 53,
      text: '顿时陷入了极度尴尬的境地',
      startTime: 107.78,
      endTime: 109.52,
      start: '00:01:47,780',
      end: '00:01:49,520',
      translatedText: 'Lập tức rơi vào tình thế cực kỳ khó xử',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\lap-tuc-roi-vao-tinh-the1748136797741.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\lap-tuc-roi-vao-tinh-the1748136797741.mp3',
      audioDuration1: 2424,
      isGenerated1: true,
    },
    {
      index: 54,
      id: 54,
      text: '为了弥补自己犯下的过错',
      startTime: 109.52,
      endTime: 110.94,
      start: '00:01:49,520',
      end: '00:01:50,940',
      translatedText: 'Để chuộc lại lỗi lầm của mình',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\de-chuoc-lai-loi-lam-cua1748136798830.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\de-chuoc-lai-loi-lam-cua1748136798830.mp3',
      audioDuration1: 1704,
      isGenerated1: true,
    },
    {
      index: 55,
      id: 55,
      text: '他急忙拿出自己珍藏许久的骨头',
      startTime: 110.94,
      endTime: 113.06,
      start: '00:01:50,940',
      end: '00:01:53,060',
      translatedText: 'Hắn vội vàng lấy ra khúc xương đã cất giữ lâu ngày',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\han-voi-vang-lay-ra-khuc1748136799775.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\han-voi-vang-lay-ra-khuc1748136799775.mp3',
      audioDuration1: 2808,
      isGenerated1: true,
    },
    {
      index: 56,
      id: 56,
      text: '试图讨好老大哥',
      startTime: 113.06,
      endTime: 114.22,
      start: '00:01:53,060',
      end: '00:01:54,220',
      translatedText: 'Cố gắng lấy lòng người anh cả',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\co-gang-lay-long-nguoi-a1748136800850.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\co-gang-lay-long-nguoi-a1748136800850.mp3',
      audioDuration1: 1545,
      isGenerated1: true,
    },
    {
      index: 57,
      id: 57,
      text: '然而',
      startTime: 114.22,
      endTime: 114.86,
      start: '00:01:54,220',
      end: '00:01:54,860',
      translatedText: 'Tuy nhiên',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\tuy-nhien1748136801661.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\tuy-nhien1748136801661.mp3',
      audioDuration1: 888,
      isGenerated1: true,
    },
    {
      index: 58,
      id: 58,
      text: '大狗却不为所动',
      startTime: 114.86,
      endTime: 116.08,
      start: '00:01:54,860',
      end: '00:01:56,080',
      translatedText: 'Đại Cẩu vẫn không mảy may động lòng',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\dai-cau-van-khong-may-ma1748136802384.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\dai-cau-van-khong-may-ma1748136802384.mp3',
      audioDuration1: 2064,
      isGenerated1: true,
    },
    {
      index: 59,
      id: 59,
      text: '心中暗自盘算',
      startTime: 116.08,
      endTime: 117.2,
      start: '00:01:56,080',
      end: '00:01:57,200',
      translatedText: 'Trong lòng âm thầm tính toán',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\trong-long-am-tham-tinh-1748136803353.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\trong-long-am-tham-tinh-1748136803353.mp3',
      audioDuration1: 1680,
      isGenerated1: true,
    },
    {
      index: 60,
      id: 60,
      text: '一定要以奇人之道',
      startTime: 117.2,
      endTime: 118.3,
      start: '00:01:57,200',
      end: '00:01:58,300',
      translatedText: 'Nhất định phải dùng cách của kẻ kỳ lạ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhat-dinh-phai-dung-cach1748136804298.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhat-dinh-phai-dung-cach1748136804298.mp3',
      audioDuration1: 1992,
      isGenerated1: true,
    },
    {
      index: 61,
      id: 61,
      text: '还治奇人之神',
      startTime: 118.3,
      endTime: 119.34,
      start: '00:01:58,300',
      end: '00:01:59,340',
      translatedText: 'Thần kỳ trị người lạ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\than-ky-tri-nguoi-la1748136805303.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\than-ky-tri-nguoi-la1748136805303.mp3',
      audioDuration1: 1368,
      isGenerated1: true,
    },
    {
      index: 62,
      id: 62,
      text: '随后猎犬们再一次发起了',
      startTime: 119.34,
      endTime: 128.76,
      start: '00:01:59,340',
      end: '00:02:08,760',
      translatedText: 'Sau đó, bầy chó săn lại một lần nữa phát động',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\sau-do-bay-cho-san-lai-m1748136806364.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\sau-do-bay-cho-san-lai-m1748136806364.mp3',
      audioDuration1: 3004.082,
      isGenerated1: true,
    },
    {
      index: 63,
      id: 63,
      text: '新一轮的猛烈进攻',
      startTime: 128.76,
      endTime: 129.76,
      start: '00:02:08,760',
      end: '00:02:09,760',
      translatedText: 'Đợt tấn công dữ dội mới',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\dot-tan-cong-du-doi-moi1748136807737.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\dot-tan-cong-du-doi-moi1748136807737.mp3',
      audioDuration1: 1645,
      isGenerated1: true,
    },
    {
      index: 64,
      id: 64,
      text: '但在狐狸面前',
      startTime: 129.76,
      endTime: 130.76,
      start: '00:02:09,760',
      end: '00:02:10,760',
      translatedText: 'Nhưng trước mặt con cáo',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-truoc-mat-con-cao1748136808958.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-truoc-mat-con-cao1748136808958.mp3',
      audioDuration1: 1205,
      isGenerated1: true,
    },
    {
      index: 65,
      id: 65,
      text: '就连德鲁比也讨不到半点好处',
      startTime: 130.76,
      endTime: 132.48,
      start: '00:02:10,760',
      end: '00:02:12,480',
      translatedText: 'Ngay cả Droopy cũng chẳng được lợi thế nào',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ngay-ca-droopy-cung-chan1748136809682.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ngay-ca-droopy-cung-chan1748136809682.mp3',
      audioDuration1: 2424,
      isGenerated1: true,
    },
    {
      index: 66,
      id: 66,
      text: '只能眼睁睁看着对方',
      startTime: 132.48,
      endTime: 133.84,
      start: '00:02:12,480',
      end: '00:02:13,840',
      translatedText: 'Chỉ có thể đứng nhìn đối phương',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chi-co-the-dung-nhin-doi1748136811190.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chi-co-the-dung-nhin-doi1748136811190.mp3',
      audioDuration1: 1728,
      isGenerated1: true,
    },
    {
      index: 67,
      id: 67,
      text: '从容消失在石洞之中',
      startTime: 133.84,
      endTime: 135.18,
      start: '00:02:13,840',
      end: '00:02:15,180',
      translatedText: 'Thản nhiên biến mất vào hang đá',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\than-nhien-bien-mat-vao-1748136812045.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\than-nhien-bien-mat-vao-1748136812045.mp3',
      audioDuration1: 1800,
      isGenerated1: true,
    },
    {
      index: 68,
      id: 68,
      text: '不甘心就此失败的猎犬们',
      startTime: 135.18,
      endTime: 136.88,
      start: '00:02:15,180',
      end: '00:02:16,880',
      translatedText: 'Không cam tâm thất bại, bầy chó săn',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khong-cam-tam-that-bai-b1748136812919.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khong-cam-tam-that-bai-b1748136812919.mp3',
      audioDuration1: 2377.1429999999996,
      isGenerated1: true,
    },
    {
      index: 69,
      id: 69,
      text: '绞尽脑汁想出了一个疯狂的计划',
      startTime: 136.88,
      endTime: 138.94,
      start: '00:02:16,880',
      end: '00:02:18,940',
      translatedText: 'Vắt óc nghĩ ra một kế hoạch điên rồ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\vat-oc-nghi-ra-mot-ke-ho1748136814289.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\vat-oc-nghi-ra-mot-ke-ho1748136814289.mp3',
      audioDuration1: 2035.0000000000002,
      isGenerated1: true,
    },
    {
      index: 70,
      id: 70,
      text: '他们点燃一枚威力巨大的鞭炮',
      startTime: 138.94,
      endTime: 140.6,
      start: '00:02:18,940',
      end: '00:02:20,600',
      translatedText: 'Chúng châm ngòi một quả pháo khổng lồ uy lực',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chung-cham-ngoi-mot-qua-1748136815328.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chung-cham-ngoi-mot-qua-1748136815328.mp3',
      audioDuration1: 2125,
      isGenerated1: true,
    },
    {
      index: 71,
      id: 71,
      text: '心怀叵测地让德鲁比送入洞中',
      startTime: 140.6,
      endTime: 142.72,
      start: '00:02:20,600',
      end: '00:02:22,720',
      translatedText: 'Với ý đồ đen tối, hắn bảo Droopy chui vào hang đá',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\voi-y-do-den-toi-han-bao1748136816373.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\voi-y-do-den-toi-han-bao1748136816373.mp3',
      audioDuration1: 3474.286,
      isGenerated1: true,
    },
    {
      index: 72,
      id: 72,
      text: '狐狸早有防备',
      startTime: 142.72,
      endTime: 143.56,
      start: '00:02:22,720',
      end: '00:02:23,560',
      translatedText: 'Con cáo đã đề phòng từ trước',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-cao-da-de-phong-tu-t1748136817899.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-cao-da-de-phong-tu-t1748136817899.mp3',
      audioDuration1: 1656,
      isGenerated1: true,
    },
    {
      index: 73,
      id: 73,
      text: '深知绝不能触发德鲁比的被动技能',
      startTime: 143.56,
      endTime: 145.74,
      start: '00:02:23,560',
      end: '00:02:25,740',
      translatedText: 'Nó hiểu rõ không được kích hoạt kỹ năng bị động của Droopy',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\no-hieu-ro-khong-duoc-ki1748136818782.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\no-hieu-ro-khong-duoc-ki1748136818782.mp3',
      audioDuration1: 3096,
      isGenerated1: true,
    },
    {
      index: 74,
      id: 74,
      text: '于是灵机一动',
      startTime: 145.74,
      endTime: 146.74,
      start: '00:02:25,740',
      end: '00:02:26,740',
      translatedText: 'Bỗng nảy ra sáng kiến',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\bong-nay-ra-sang-kien1748136820116.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\bong-nay-ra-sang-kien1748136820116.mp3',
      audioDuration1: 1488,
      isGenerated1: true,
    },
    {
      index: 75,
      id: 75,
      text: '用一根骨头精心伪装后',
      startTime: 146.74,
      endTime: 148.26,
      start: '00:02:26,740',
      end: '00:02:28,260',
      translatedText: 'Dùng một khúc xương ngụy trang tinh vi',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\dung-mot-khuc-xuong-nguy1748136820955.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\dung-mot-khuc-xuong-nguy1748136820955.mp3',
      audioDuration1: 2055,
      isGenerated1: true,
    },
    {
      index: 76,
      id: 76,
      text: '满脸堆笑地递给了德鲁比',
      startTime: 148.26,
      endTime: 149.88,
      start: '00:02:28,260',
      end: '00:02:29,880',
      translatedText: 'Rồi nở nụ cười giả tạo đưa cho Droopy',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\roi-no-nu-cuoi-gia-tao-d1748136821867.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\roi-no-nu-cuoi-gia-tao-d1748136821867.mp3',
      audioDuration1: 2616,
      isGenerated1: true,
    },
    {
      index: 77,
      id: 77,
      text: '看到骨头',
      startTime: 149.88,
      endTime: 150.48,
      start: '00:02:29,880',
      end: '00:02:30,480',
      translatedText: 'Nhìn thấy khúc xương',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhin-thay-khuc-xuong1748136823031.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhin-thay-khuc-xuong1748136823031.mp3',
      audioDuration1: 1368,
      isGenerated1: true,
    },
    {
      index: 78,
      id: 78,
      text: '猎犬们瞬间被吸引',
      startTime: 150.48,
      endTime: 151.94,
      start: '00:02:30,480',
      end: '00:02:31,940',
      translatedText: 'Bầy chó săn lập tức bị thu hút',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\bay-cho-san-lap-tuc-bi-t1748136823808.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\bay-cho-san-lap-tuc-bi-t1748136823808.mp3',
      audioDuration1: 1896,
      isGenerated1: true,
    },
    {
      index: 79,
      id: 79,
      text: '忘记了抓狐狸的任务',
      startTime: 151.94,
      endTime: 153.18,
      start: '00:02:31,940',
      end: '00:02:33,180',
      translatedText: 'Quên mất nhiệm vụ bắt cáo',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\quen-mat-nhiem-vu-bat-ca1748136824819.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\quen-mat-nhiem-vu-bat-ca1748136824819.mp3',
      audioDuration1: 1464,
      isGenerated1: true,
    },
    {
      index: 80,
      id: 80,
      text: '像一群恶狼般疯狂争抢起来',
      startTime: 153.18,
      endTime: 155.12,
      start: '00:02:33,180',
      end: '00:02:35,120',
      translatedText: 'Như một đàn sói đói tranh giành điên cuồng',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhu-mot-dan-soi-doi-tran1748136825568.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhu-mot-dan-soi-doi-tran1748136825568.mp3',
      audioDuration1: 2105,
      isGenerated1: true,
    },
    {
      index: 81,
      id: 81,
      text: '全然不顾公平分配',
      startTime: 155.12,
      endTime: 156.28,
      start: '00:02:35,120',
      end: '00:02:36,280',
      translatedText: 'Hoàn toàn không quan tâm đến việc phân chia công bằng',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\hoan-toan-khong-quan-tam1748136826455.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\hoan-toan-khong-quan-tam1748136826455.mp3',
      audioDuration1: 2592,
      isGenerated1: true,
    },
    {
      index: 82,
      id: 82,
      text: '殊不知',
      startTime: 156.28,
      endTime: 156.88,
      start: '00:02:36,280',
      end: '00:02:36,880',
      translatedText: 'Không ngờ rằng',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khong-ngo-rang1748136827524.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khong-ngo-rang1748136827524.mp3',
      audioDuration1: 1056,
      isGenerated1: true,
    },
    {
      index: 83,
      id: 83,
      text: '这正是狐狸精心设下的陷阱',
      startTime: 156.88,
      endTime: 158.78,
      start: '00:02:36,880',
      end: '00:02:38,780',
      translatedText: 'Đây chính là cái bẫy tinh vi do con cáo giăng ra',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\day-chinh-la-cai-bay-tin1748136828099.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\day-chinh-la-cai-bay-tin1748136828099.mp3',
      audioDuration1: 2736,
      isGenerated1: true,
    },
    {
      index: 84,
      id: 84,
      text: '即便遭遇如此挫折',
      startTime: 158.78,
      endTime: 171.79,
      start: '00:02:38,780',
      end: '00:02:51,790',
      translatedText: 'Dù gặp phải thất bại như vậy',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\du-gap-phai-that-bai-nhu1748136829360.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\du-gap-phai-that-bai-nhu1748136829360.mp3',
      audioDuration1: 1656,
      isGenerated1: true,
    },
    {
      index: 85,
      id: 85,
      text: '猎犬们仍不死心',
      startTime: 171.79,
      endTime: 173.11,
      start: '00:02:51,790',
      end: '00:02:53,110',
      translatedText: 'Bọn chó săn vẫn không chịu từ bỏ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\bon-cho-san-van-khong-ch1748136830380.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\bon-cho-san-van-khong-ch1748136830380.mp3',
      audioDuration1: 1865,
      isGenerated1: true,
    },
    {
      index: 86,
      id: 86,
      text: '又找来一枚巨型鞭炮',
      startTime: 173.11,
      endTime: 174.41,
      start: '00:02:53,110',
      end: '00:02:54,410',
      translatedText: 'Lại tìm thêm một quả pháo khổng lồ',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\lai-tim-them-mot-qua-pha1748136831368.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\lai-tim-them-mot-qua-pha1748136831368.mp3',
      audioDuration1: 1905,
      isGenerated1: true,
    },
    {
      index: 87,
      id: 87,
      text: '妄图直接将山洞移为平地',
      startTime: 174.41,
      endTime: 176.35,
      start: '00:02:54,410',
      end: '00:02:56,350',
      translatedText: 'Toan tính dùng nó san bằng hang đá',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\toan-tinh-dung-no-san-ba1748136832385.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\toan-tinh-dung-no-san-ba1748136832385.mp3',
      audioDuration1: 2016,
      isGenerated1: true,
    },
    {
      index: 88,
      id: 88,
      text: '然而',
      startTime: 176.35,
      endTime: 176.93,
      start: '00:02:56,350',
      end: '00:02:56,930',
      translatedText: 'Thế nhưng',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\the-nhung1748136833467.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\the-nhung1748136833467.mp3',
      audioDuration1: 912,
      isGenerated1: true,
    },
    {
      index: 89,
      id: 89,
      text: '它们的一举一动',
      startTime: 176.93,
      endTime: 178.07,
      start: '00:02:56,930',
      end: '00:02:58,070',
      translatedText: 'Mọi hành động của chúng',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\moi-hanh-dong-cua-chung1748136834116.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\moi-hanh-dong-cua-chung1748136834116.mp3',
      audioDuration1: 1320,
      isGenerated1: true,
    },
    {
      index: 90,
      id: 90,
      text: '都在狐狸的预料之中',
      startTime: 178.07,
      endTime: 179.23,
      start: '00:02:58,070',
      end: '00:02:59,230',
      translatedText: 'Đều nằm trong dự đoán của con cáo',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\deu-nam-trong-du-doan-cu1748136834865.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\deu-nam-trong-du-doan-cu1748136834865.mp3',
      audioDuration1: 1992,
      isGenerated1: true,
    },
    {
      index: 91,
      id: 91,
      text: '成功解决掉对手后',
      startTime: 179.23,
      endTime: 183.85,
      start: '00:02:59,230',
      end: '00:03:03,850',
      translatedText: 'Sau khi giải quyết đối thủ thành công',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\sau-khi-giai-quyet-doi-t1748136835818.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\sau-khi-giai-quyet-doi-t1748136835818.mp3',
      audioDuration1: 1995,
      isGenerated1: true,
    },
    {
      index: 92,
      id: 92,
      text: '狐狸神态悠然',
      startTime: 183.85,
      endTime: 184.95,
      start: '00:03:03,850',
      end: '00:03:04,950',
      translatedText: 'Cáo tỏ ra thư thái',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\cao-to-ra-thu-thai1748136836700.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\cao-to-ra-thu-thai1748136836700.mp3',
      audioDuration1: 1416,
      isGenerated1: true,
    },
    {
      index: 93,
      id: 93,
      text: '迈着轻快的步伐准备回家',
      startTime: 184.95,
      endTime: 186.67,
      start: '00:03:04,950',
      end: '00:03:06,670',
      translatedText: 'Bước những bước đi nhẹ nhàng chuẩn bị về nhà',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\buoc-nhung-buoc-di-nhe-n1748136837541.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\buoc-nhung-buoc-di-nhe-n1748136837541.mp3',
      audioDuration1: 2400,
      isGenerated1: true,
    },
    {
      index: 94,
      id: 94,
      text: '可当它打开门',
      startTime: 186.67,
      endTime: 187.63,
      start: '00:03:06,670',
      end: '00:03:07,630',
      translatedText: 'Nhưng khi nó mở cửa',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-khi-no-mo-cua1748136838726.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhung-khi-no-mo-cua1748136838726.mp3',
      audioDuration1: 1320,
      isGenerated1: true,
    },
    {
      index: 95,
      id: 95,
      text: '却瞧见一脸无奈的德鲁比',
      startTime: 187.63,
      endTime: 189.27,
      start: '00:03:07,630',
      end: '00:03:09,270',
      translatedText: 'Lại thấy Droopy với vẻ mặt bất lực',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\lai-thay-droopy-voi-ve-m1748136839515.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\lai-thay-droopy-voi-ve-m1748136839515.mp3',
      audioDuration1: 2184,
      isGenerated1: true,
    },
    {
      index: 96,
      id: 96,
      text: '正站在门外',
      startTime: 189.27,
      endTime: 190.11,
      start: '00:03:09,270',
      end: '00:03:10,110',
      translatedText: 'Đang đứng ngoài cửa',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\dang-dung-ngoai-cua1748136840593.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\dang-dung-ngoai-cua1748136840593.mp3',
      audioDuration1: 1075,
      isGenerated1: true,
    },
    {
      index: 97,
      id: 97,
      text: '看着眼前这只沙皮狗',
      startTime: 190.11,
      endTime: 191.45,
      start: '00:03:10,110',
      end: '00:03:11,450',
      translatedText: 'Nhìn chú chó xếp nếp trước mắt',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhin-chu-cho-xep-nep-tru1748136841259.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\nhin-chu-cho-xep-nep-tru1748136841259.mp3',
      audioDuration1: 1655,
      isGenerated1: true,
    },
    {
      index: 98,
      id: 98,
      text: '狐狸一开始心里是抵触的',
      startTime: 191.45,
      endTime: 193.05,
      start: '00:03:11,450',
      end: '00:03:13,050',
      translatedText: 'Ban đầu cáo trong lòng rất chống đối',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ban-dau-cao-trong-long-r1748136842096.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ban-dau-cao-trong-long-r1748136842096.mp3',
      audioDuration1: 1795,
      isGenerated1: true,
    },
    {
      index: 99,
      id: 99,
      text: '并不打算配合他',
      startTime: 193.05,
      endTime: 194.07,
      start: '00:03:13,050',
      end: '00:03:14,070',
      translatedText: 'Không có ý định hợp tác với hắn',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khong-co-y-dinh-hop-tac-1748136842956.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khong-co-y-dinh-hop-tac-1748136842956.mp3',
      audioDuration1: 1715,
      isGenerated1: true,
    },
    {
      index: 100,
      id: 100,
      text: '然而',
      startTime: 194.07,
      endTime: 194.65,
      start: '00:03:14,070',
      end: '00:03:14,650',
      translatedText: 'Tuy nhiên',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\tuy-nhien1748136843886.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\tuy-nhien1748136843886.mp3',
      audioDuration1: 805,
      isGenerated1: true,
    },
    {
      index: 101,
      id: 101,
      text: '当德鲁比抛出一只狐狸',
      startTime: 194.65,
      endTime: 196.07,
      start: '00:03:14,650',
      end: '00:03:16,070',
      translatedText: 'Khi Droopy ném ra một con cáo',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khi-droopy-nem-ra-mot-co1748136844478.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khi-droopy-nem-ra-mot-co1748136844478.mp3',
      audioDuration1: 2280,
      isGenerated1: true,
    },
    {
      index: 102,
      id: 102,
      text: '换一块牛排这极具诱惑的条件时',
      startTime: 196.07,
      endTime: 197.97,
      start: '00:03:16,070',
      end: '00:03:17,970',
      translatedText: 'Đổi lấy miếng bò bít tết chiến rìu - điều kiện quá hấp dẫn',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\doi-lay-mieng-bo-bit-tet1748136845550.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\doi-lay-mieng-bo-bit-tet1748136845550.mp3',
      audioDuration1: 2945,
      isGenerated1: true,
    },
    {
      index: 103,
      id: 103,
      text: '狐狸的眼睛瞬间亮了起来',
      startTime: 197.97,
      endTime: 199.69,
      start: '00:03:17,970',
      end: '00:03:19,690',
      translatedText: 'Mắt con cáo lập tức sáng rực lên',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\mat-con-cao-lap-tuc-sang1748136846667.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\mat-con-cao-lap-tuc-sang1748136846667.mp3',
      audioDuration1: 2040,
      isGenerated1: true,
    },
    {
      index: 104,
      id: 104,
      text: '刚才的不情愿一扫而空',
      startTime: 199.69,
      endTime: 200.95,
      start: '00:03:19,690',
      end: '00:03:20,950',
      translatedText: 'Sự miễn cưỡng ban đầu biến mất không dấu vết',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\su-mien-cuong-ban-dau-bi1748136848109.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\su-mien-cuong-ban-dau-bi1748136848109.mp3',
      audioDuration1: 2280,
      isGenerated1: true,
    },
    {
      index: 105,
      id: 105,
      text: '毫不犹豫地就点头答应合作',
      startTime: 200.95,
      endTime: 202.79,
      start: '00:03:20,950',
      end: '00:03:22,790',
      translatedText: 'Không chút do dự liền gật đầu đồng ý hợp tác',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khong-chut-do-du-lien-ga1748136849089.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khong-chut-do-du-lien-ga1748136849089.mp3',
      audioDuration1: 2335,
      isGenerated1: true,
    },
    {
      index: 106,
      id: 106,
      text: '紧接着',
      startTime: 202.79,
      endTime: 203.41,
      start: '00:03:22,790',
      end: '00:03:23,410',
      translatedText: 'Ngay sau đó',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ngay-sau-do1748136850068.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ngay-sau-do1748136850068.mp3',
      audioDuration1: 945,
      isGenerated1: true,
    },
    {
      index: 107,
      id: 107,
      text: '狐狸兴高采烈地跑去',
      startTime: 203.41,
      endTime: 204.87,
      start: '00:03:23,410',
      end: '00:03:24,870',
      translatedText: 'Con cáo hớn hở chạy đi',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-cao-hon-ho-chay-di1748136850659.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\con-cao-hon-ho-chay-di1748136850659.mp3',
      audioDuration1: 1512,
      isGenerated1: true,
    },
    {
      index: 108,
      id: 108,
      text: '将这个喜讯分享给亲朋好友',
      startTime: 204.87,
      endTime: 206.67,
      start: '00:03:24,870',
      end: '00:03:26,670',
      translatedText: 'Mang tin vui này chia sẻ với họ hàng bạn bè',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\mang-tin-vui-nay-chia-se1748136851450.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\mang-tin-vui-nay-chia-se1748136851450.mp3',
      audioDuration1: 2616,
      isGenerated1: true,
    },
    {
      index: 109,
      id: 109,
      text: '不多时',
      startTime: 206.67,
      endTime: 207.25,
      start: '00:03:26,670',
      end: '00:03:27,250',
      translatedText: 'Chẳng bao lâu sau',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chang-bao-lau-sau1748136852581.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\chang-bao-lau-sau1748136852581.mp3',
      audioDuration1: 1296,
      isGenerated1: true,
    },
    {
      index: 110,
      id: 110,
      text: '一只阵容庞大的狐狸大军',
      startTime: 207.25,
      endTime: 208.93,
      start: '00:03:27,250',
      end: '00:03:28,930',
      translatedText: 'Một đội quân cáo hùng hậu đã tập hợp',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\mot-doi-quan-cao-hung-ha1748136853508.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\mot-doi-quan-cao-hung-ha1748136853508.mp3',
      audioDuration1: 2016,
      isGenerated1: true,
    },
    {
      index: 111,
      id: 111,
      text: '在德鲁比的引领下',
      startTime: 208.93,
      endTime: 210.03,
      start: '00:03:28,930',
      end: '00:03:30,030',
      translatedText: 'Dưới sự dẫn đường của Droopy',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\duoi-su-dan-duong-cua-dr1748136854456.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\duoi-su-dan-duong-cua-dr1748136854456.mp3',
      audioDuration1: 1944,
      isGenerated1: true,
    },
    {
      index: 112,
      id: 112,
      text: '浩浩荡荡地朝着农场主的家行进',
      startTime: 210.03,
      endTime: 212.19,
      start: '00:03:30,030',
      end: '00:03:32,190',
      translatedText: 'Đoàn người hùng hậu tiến thẳng về nhà chủ nông trại',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\doan-nguoi-hung-hau-tien1748136855541.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\doan-nguoi-hung-hau-tien1748136855541.mp3',
      audioDuration1: 2544,
      isGenerated1: true,
    },
    {
      index: 113,
      id: 113,
      text: '最终',
      startTime: 212.19,
      endTime: 212.67,
      start: '00:03:32,190',
      end: '00:03:32,670',
      translatedText: 'Cuối cùng',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\cuoi-cung1748136856571.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\cuoi-cung1748136856571.mp3',
      audioDuration1: 840,
      isGenerated1: true,
    },
    {
      index: 114,
      id: 114,
      text: '这场充满戏剧性与波折的较量',
      startTime: 212.67,
      endTime: 214.67,
      start: '00:03:32,670',
      end: '00:03:34,670',
      translatedText: 'Cuộc đối đầu đầy kịch tính và sóng gió này',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\cuoc-doi-dau-day-kich-ti1748136857232.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\cuoc-doi-dau-day-kich-ti1748136857232.mp3',
      audioDuration1: 2328,
      isGenerated1: true,
    },
    {
      index: 115,
      id: 115,
      text: '以狐狸和德鲁比双双获利的局面',
      startTime: 214.67,
      endTime: 216.69,
      start: '00:03:34,670',
      end: '00:03:36,690',
      translatedText: 'Đã kết thúc với kết cục đôi bên cùng có lợi cho cả cáo và Droopy',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\da-ket-thuc-voi-ket-cuc-1748136858252.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\da-ket-thuc-voi-ket-cuc-1748136858252.mp3',
      audioDuration1: 3576,
      isGenerated1: true,
    },
    {
      index: 116,
      id: 116,
      text: '完美画上句号',
      startTime: 216.69,
      endTime: 217.69,
      start: '00:03:36,690',
      end: '00:03:37,690',
      translatedText: 'Khép lại một cách hoàn hảo',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khep-lai-mot-cach-hoan-h1748136859687.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\khep-lai-mot-cach-hoan-h1748136859687.mp3',
      audioDuration1: 1512,
      isGenerated1: true,
    },
    {
      index: 117,
      id: 117,
      text: '他们携手共同谱写了一段',
      startTime: 217.69,
      endTime: 219.23,
      start: '00:03:37,690',
      end: '00:03:39,230',
      translatedText: 'Họ cùng nhau viết nên',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ho-cung-nhau-viet-nen1748136860526.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\ho-cung-nhau-viet-nen1748136860526.mp3',
      audioDuration1: 1512,
      isGenerated1: true,
    },
    {
      index: 118,
      id: 118,
      text: '让人忍俊不禁的传奇故事',
      startTime: 219.23,
      endTime: 220.87,
      start: '00:03:39,230',
      end: '00:03:40,870',
      translatedText: 'Một câu chuyện truyền kỳ khiến người ta không nhịn được cười',
      status: 'translated',
      selectedSpeaker: 'tts.other.BV075_streaming',
      speechRate: 0,
      audioUrl: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\mot-cau-chuyen-truyen-ky1748136861328.mp3',
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1,
      audioUrl1: 'file://I:\\ReviewDao\\phim\\hhinh_audio\\mot-cau-chuyen-truyen-ky1748136861328.mp3',
      audioDuration1: 2568,
      isGenerated1: true,
    },
  ],
  srtPath: 'I:\\ReviewDao\\phim\\hhinh.srt',
  blurAreas: [
    {
      type: 'blur',
      x: 13,
      y: 193,
      width: 119,
      height: 98,
      timeStart: 0,
      timeEnd: 220.966667,
    },
    {
      type: 'delogo',
      x: 472.40625,
      y: 123,
      width: 129,
      height: 140,
      timeStart: 0,
      timeEnd: 220.966667,
    },
    {
      type: 'subtitle',
      x: 23.40625,
      y: 391,
      width: 574,
      height: 50,
      timeStart: 0,
      timeEnd: 220.966667,
    },
  ],
  timeRange: {
    start: 0,
    end: 220.966667,
  },
  options: {
    textAnimation: {
      enabled: true,
      fontSize: 24,
      color: '#ffffff',
      value: 'Hello World',
    },
    textSubtitle: {
      enabled: true,
      fontSize: 24,
      color: '#ffffff',
    },
    logo: {
      enabled: false,
      position: 'bottom-right',
      size: 'medium',
    },
    audio: {
      backgroundMusic: {
        enabled: false,
        volume: 0.3,
      },
      originalVolume: 0.8,
      holdOriginalAudio: false,
      holdMusicOnly: false,
    },
    output: {
      quality: '1080p',
      frameRate: 30,
    },
  },
};


async function holdMusicOnlyHandler(event, videoInput, outputDir) {
  const htdemucsDir = path.basename(videoInput, path.extname(videoInput));
  const noVolcalAudio = path.join(outputDir, 'htdemucs', htdemucsDir, 'no_vocals.wav');
  const outputPath = path.join(outputDir, 'output_music_only.mp4');
  const cmd = `ffmpeg -i "${videoInput}" -i "${noVolcalAudio}" -map 0:v -map 1:a -c:v copy -c:a aac -shortest -y "${outputPath}"`;
  await execPromise(cmd);
  return outputPath;
}

async function textAnimationHandler(event, videoInput, outputDir, textAnimationOptions) {
  const { fontSize, color, value, opacity = 0.5 } = textAnimationOptions;
  const outputFinal = path.basename(videoInput, path.extname(videoInput));
  const outputVideo = path.join(outputDir, `${outputFinal}_text_animation.mp4`);
  const encoder = await getEncoder();
  const cmd = `ffmpeg -i "${videoInput}" -vf "drawtext=text='${value}':fontsize=${fontSize}:fontcolor=${color}@${opacity}:x='(w-text_w)*abs(mod(t\\,180)-90)/90':y='(h-text_h)*abs(mod(t\\,180)-90)/90'" -c:v ${encoder} -c:a copy -y "${outputVideo}"`;
  await execPromise(cmd);
  console.log('Text animation applied', outputVideo);
  return outputVideo;
}

function generateDirectionExprSlow(start, segment, directionType = "random") {
  const half = segment / 2;
  const progress = `(abs(mod(t-${start}\\,${segment})-${half})/${half})`;

  const directionMap = {
    updown: [2, 3],
    leftright: [0, 1],
    diagonal: [4, 5, 6, 7],
    all: [0, 1, 2, 3, 4, 5, 6, 7],
    random: [0, 1, 2, 3, 4, 5, 6, 7],
  };

  const validDirections = directionMap[directionType] || directionMap.random;
  const direction = validDirections[Math.floor(Math.random() * validDirections.length)];

  switch (direction) {
    case 0: return { x: `(w-text_w)*${progress}`, y: `(h-text_h)/2` }; // Left to Right
    case 1: return { x: `(w-text_w)*(1-${progress})`, y: `(h-text_h)/2` }; // Right to Left
    case 2: return { x: `(w-text_w)/2`, y: `(h-text_h)*${progress}` }; // Top to Bottom
    case 3: return { x: `(w-text_w)/2`, y: `(h-text_h)*(1-${progress})` }; // Bottom to Top
    case 4: return { x: `(w-text_w)*${progress}`, y: `(h-text_h)*${progress}` }; // TL → BR
    case 5: return { x: `(w-text_w)*(1-${progress})`, y: `(h-text_h)*${progress}` }; // TR → BL
    case 6: return { x: `(w-text_w)*${progress}`, y: `(h-text_h)*(1-${progress})` }; // BL → TR
    case 7: return { x: `(w-text_w)*(1-${progress})`, y: `(h-text_h)*(1-${progress})` }; // BR → TL
  }
}


async function textAnimationRandomHandler(event, videoInput, outputDir, textAnimationOptions, videoInfo) {
  const { fontSize, color, value, opacity = 0.5, directionType = 'random' } = textAnimationOptions;
  const outputFinal = path.basename(videoInput, path.extname(videoInput));
  const outputVideo = path.join(outputDir, `${outputFinal}_text_animation.mp4`);
  const { duration } = videoInfo;

  const segment = 120; // 120s mỗi đoạn
  const count = Math.ceil(duration / segment);
  let filters = '';
  const fadeTime = 1; 

  for (let i = 0; i < count; i++) {
    const start = i * segment;
    const end = Math.min((i + 1) * segment, duration);
    const { x, y } = generateDirectionExprSlow(start, segment, directionType);

    const alpha = `if(lt(t\\,${start}+${fadeTime}),(t-${start})/${fadeTime},if(lt(t\\,${end}-${fadeTime}),1,(${end}-t)/${fadeTime}))`;


    filters += `drawtext=text='${value}':fontsize=${fontSize}:fontcolor=${color}@${opacity}:alpha='${alpha}':`;
    filters += `x='${x}':y='${y}':enable='between(t\\,${start}\\,${end})',`;
  }

  if (!filters) {
    filters = `drawtext=text='${value}':fontsize=${fontSize}:fontcolor=${color}@${opacity}:x='(w-text_w)*abs(mod(t\\,180)-90)/90':y='(h-text_h)*abs(mod(t\\,180)-90)/90'`;
  }

  filters = filters.replace(/,$/, '');

  const filterFile = path.join(outputDir, 'filter.txt');
  fs.writeFileSync(filterFile, filters);
  console.log('🎞️ Filter saved:\n', filters);
  const encoder = await getEncoder();

  const ffmpegCmd = `ffmpeg -i "${videoInput}" -filter_complex_script "${filterFile}" -c:v ${encoder} -c:a copy -y "${outputVideo}"`;
  await execPromise(ffmpegCmd);
  fs.unlinkSync(filterFile);
  console.log('✅ Text animation applied:', outputVideo);
  return outputVideo;
}





async function blurAreasHandler(event, videoInput, outputDir, blurAreas, videoInfo) {
  const outputFinal = path.basename(videoInput, path.extname(videoInput));
  const outputVideo = path.join(outputDir, `${outputFinal}_blur.mp4`);
  const { fps, total_frames, duration } = videoInfo;
  const getFrameNumber = (timeInSeconds) => {
    const frame = Math.round(timeInSeconds * fps);
    // Đảm bảo frame number không vượt quá tổng số frame
    return Math.min(frame, total_frames - 1);
  };
  const filters = blurAreas.map((box, index) => {
    const videoCoords = box.videoCoords;
    const startFrame = getFrameNumber(box.timeStart);
    const endFrame = box.timeEnd === duration ? total_frames - 1 : getFrameNumber(box.timeEnd);

    let enableExpr;
    if (box.timeStart === 0 && box.timeEnd === duration) {
      enableExpr = '1'; // Luôn bật nếu áp dụng cho toàn bộ video
    } else if (box.timeStart === 0) {
      enableExpr = `lte(n,${endFrame})`; // Từ đầu đến frame cụ thể
    } else if (box.timeEnd === duration) {
      enableExpr = `gte(n,${startFrame})`; // Từ frame cụ thể đến cuối
    } else {
      enableExpr = `between(n,${startFrame},${endFrame})`; // Giữa khoảng frame
    }

    return `delogo=enable='${enableExpr}':x=${Math.round(videoCoords.x)}:y=${Math.round(videoCoords.y)}:w=${Math.round(
      videoCoords.width,
    )}:h=${Math.round(videoCoords.height)}`;
  });
  const filterComplex = filters.join(',');
  console.log(filterComplex);
const encoder = await getEncoder();
  const cmd = `ffmpeg -i "${videoInput}" -vf "${filterComplex}" -c:v ${encoder} -c:a copy -y "${outputVideo}"`;
  await execPromise(cmd);
  return outputVideo;
}

async function processVideoWithOptions(event, renderConfig) {
  try {
    let videoInput = renderConfig.srtPath.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4');
    const videoInfo = await getVideoInfo(event, videoInput);
    const outputDir = path.dirname(videoInput);
    const outputFinal = path.basename(videoInput, path.extname(videoInput)) + '_final.mp4';
    const outputVideo = path.join(outputDir, outputFinal);
    const holdMusicOnly = renderConfig?.options?.audio?.holdMusicOnly || false;
    const textAnimation = renderConfig?.options?.textAnimation?.enabled || false;
    const blurAreas = renderConfig?.blurAreas || [];

    if (holdMusicOnly) {
      console.log('Extracting music only');
      videoInput = await holdMusicOnlyHandler(event, videoInput, outputDir);
      console.log('Music only extracted', videoInput);
    }
    if (blurAreas.length > 0 && renderConfig?.options.textSubtitle?.enabled) {
      console.log('Applying blur areas');
      videoInput = await blurAreasHandler(event, videoInput, outputDir, blurAreas, videoInfo);
      console.log('Blur areas applied', videoInput);
    }
    if (textAnimation) {
      console.log('Applying text animation');
      videoInput = await textAnimationRandomHandler(event, videoInput, outputDir, renderConfig?.options?.textAnimation,videoInfo);
    }
    const result = await processVideoSimplified(
      event,
      videoInput,
      renderConfig.srtItems,
      outputDir,
      outputVideo,
      renderConfig.options,
    );
    // console.log('Result:', result);
    return result;
  } catch (error) {
    console.error('Error processing video with options:', error);
    throw error;
  }
}

const execPromise = (cmd) =>
  new Promise((resolve, reject) => {
    exec(cmd, (err, stdout, stderr) => {
      if (err) {
        console.error('❌ FFmpeg error:', err);
        console.error(stderr);
        return reject(err);
      }
      resolve();
    });
  });

module.exports = {
  processVideoWithOptions,
};
