<template>
  <!-- Container chiếm full height với layout pattern chuẩn -->
  <div class="flex-1 flex flex-col bg-gray-900 text-white min-h-0">
    <!-- Main container với resizable horizontal split -->
    <div class="flex flex-col flex-1 min-h-0">
      <!-- Top section (resizable) -->
      <div
        class="flex border-b border-gray-600"
        :style="{ height: topHeight + 'px' }"
      >
        <!-- Left panel - Video -->
        <div
          class="border-r border-gray-600 bg-gray-800 flex items-center justify-center text-white"
          :style="{ width: leftWidth + 'px' }"
        >
          <div class="text-center">
            <div class="text-4xl mb-2">🎥</div>
            <div class="text-sm opacity-75">Video Component</div>
            <div class="text-xs opacity-50 mt-1">Ngắn hơn</div>
          </div>
        </div>

        <!-- Vertical resizer -->
        <div
          class="w-1 bg-gray-600 hover:bg-cyan-500 cursor-col-resize transition-colors duration-200"
          @mousedown="startVerticalResize"
        ></div>

        <!-- Right panel - Subtitle -->
        <div class="flex-1 min-h-0 h-full bg-gray-700 w-full">
          <!-- <div class="text-center">
            <div class="text-4xl mb-2">📝</div>
            <div class="text-sm text-gray-200">Subtitle Component</div>
            <div class="text-xs text-gray-400 mt-1">Dài hơn</div>
          </div> -->
          <SubtitleTranslatorTerm />
        </div>
      </div>

      <!-- Horizontal resizer -->
      <div
        class="h-1 bg-gray-600 hover:bg-cyan-500 cursor-row-resize transition-colors duration-200"
        @mousedown="startHorizontalResize"
      ></div>

      <!-- Bottom section - Timeline -->
      <div class="flex-1 bg-gray-750 flex items-center justify-center min-h-0 h-full">
        <div class="text-center">
          <div class="text-4xl mb-2">⏱️</div>
          <div class="text-sm text-gray-200">Timeline Component</div>
          <div class="text-xs text-gray-400 mt-1">Chiếm hết phần dưới</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import SubtitleTranslatorTerm from '../SubtitleTranslatorTerm.vue'

// Reactive variables for panel sizes
const topHeight = ref(400) // Giảm xuống để fit better
const leftWidth = ref(350) // Giảm xuống để fit better

// Resize state
const isResizing = ref(false)
const resizeType = ref(null) // 'horizontal' or 'vertical'
const startY = ref(0)
const startX = ref(0)
const startTopHeight = ref(0)
const startLeftWidth = ref(0)

// Container dimensions
const containerHeight = ref(800) // Tăng lên để match với layout mới
const containerWidth = ref(1200)

// Initialize container size
onMounted(() => {
  nextTick(() => {
    updateContainerSize()
  })
  window.addEventListener('resize', updateContainerSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateContainerSize)
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})

const updateContainerSize = () => {
  // Get the actual available space from the tab pane container
  const tabPaneElement = document.querySelector('.ant-tabs-tabpane-active')
  if (tabPaneElement) {
    containerHeight.value = tabPaneElement.clientHeight
    containerWidth.value = tabPaneElement.clientWidth
  } else {
    // Fallback to window size calculation
    containerHeight.value = window.innerHeight - 120 // Account for header + tab bar
    containerWidth.value = window.innerWidth
  }

  // Ensure panels don't exceed container bounds
  if (topHeight.value > containerHeight.value * 0.8) {
    topHeight.value = Math.max(200, containerHeight.value * 0.6)
  }
  if (leftWidth.value > containerWidth.value * 0.8) {
    leftWidth.value = Math.max(200, containerWidth.value * 0.4)
  }
}

// Start horizontal resize (top/bottom split)
const startHorizontalResize = (e) => {
  e.preventDefault()
  isResizing.value = true
  resizeType.value = 'horizontal'
  startY.value = e.clientY
  startTopHeight.value = topHeight.value

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = 'row-resize'
  document.body.style.userSelect = 'none'
}

// Start vertical resize (left/right split)
const startVerticalResize = (e) => {
  e.preventDefault()
  isResizing.value = true
  resizeType.value = 'vertical'
  startX.value = e.clientX
  startLeftWidth.value = leftWidth.value

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
}

const handleMouseMove = (e) => {
  if (!isResizing.value) return

  if (resizeType.value === 'horizontal') {
    const deltaY = e.clientY - startY.value
    const newHeight = startTopHeight.value + deltaY

    // Constrain height between 200px and 80% of container height
    const minHeight = 200
    const maxHeight = containerHeight.value * 0.8

    topHeight.value = Math.max(minHeight, Math.min(maxHeight, newHeight))
  } else if (resizeType.value === 'vertical') {
    const deltaX = e.clientX - startX.value
    const newWidth = startLeftWidth.value + deltaX

    // Constrain width between 200px and 80% of container width
    const minWidth = 200
    const maxWidth = containerWidth.value * 0.8

    leftWidth.value = Math.max(minWidth, Math.min(maxWidth, newWidth))
  }
}

const handleMouseUp = () => {
  isResizing.value = false
  resizeType.value = null

  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}
</script>

<style scoped>
/* Custom background color để match với dark theme */
.bg-gray-750 {
  background-color: #374151;
}

/* Smooth transitions when not resizing */
.transition-all {
  transition: all 0.2s ease;
}

/* Prevent text selection during resize */
.select-none {
  user-select: none;
}

/* Custom scrollbar for panels if needed */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #1F1F1F;
}

::-webkit-scrollbar-thumb {
  background: #666666;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #888888;
}
</style>