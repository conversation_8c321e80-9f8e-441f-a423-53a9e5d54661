<template>
        <a-form-item label="Text Animation/watermark">
          <a-checkbox v-model:checked="renderOptions.showText">
            Show text Animation
          </a-checkbox>
          <div v-if="renderOptions.showText" class="mt-2">

            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="Text">
                  <a-input
                    v-model:value="renderOptions.textValue"
                    type="text"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="5">
                <a-form-item label="Font Size">
                  <a-input-number
                    v-model:value="renderOptions.fontSize"
                    :min="12"
                    :max="72"
                    addon-after="px"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="5">
                <a-form-item label="Text Color">
                  <a-input
                    v-model:value="renderOptions.textColor"
                    type="color"
                  />
                </a-form-item>
              </a-col>
              <!-- opacity -->
              <a-col :span="5">
                <a-form-item label="Opacity">
                  <a-slider
                    v-model:value="renderOptions.textOpacity"
                    :min="0"
                    :max="100"
                  />
                </a-form-item>
              </a-col>
              <!-- direction -->
              <a-col :span="6">
                <a-form-item label="Hướng di chuyển">
                  <a-select v-model:value="renderOptions.textDirection">
                    <a-select-option value="random">Random</a-select-option>
                    <a-select-option value="updown">Up & Down</a-select-option>
                    <a-select-option value="leftright">Left & Right</a-select-option>
                    <a-select-option value="diagonal">Diagonal</a-select-option>
                    <a-select-option value="all">All</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

            </a-row>
          </div>
        </a-form-item>
</template>
<script setup>
import { useSubtitleStore } from '@/stores/subtitle-store';

const subtitleStore = useSubtitleStore();
const renderOptions = subtitleStore.renderOptions;
</script>



