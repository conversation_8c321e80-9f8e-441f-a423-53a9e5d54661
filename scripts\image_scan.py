import cv2
import sys
from paddleocr import PaddleOCR

def parse_crop_arg(arg):
    try:
        parts = [float(x) for x in arg.split(',')]
        if len(parts) != 4:
            raise ValueError("Crop must have 4 comma-separated values")
        return parts
    except Exception as e:
        print("Invalid crop format. Use: --crop x1,y1,x2,y2")
        sys.exit(1)

# Parse args
if len(sys.argv) < 2:
    print("Usage: python ocr_crop.py path_to_image [--crop x1,y1,x2,y2]")
    sys.exit(1)

image_path = sys.argv[1]

# Check if --crop is passed
crop_values = None
if len(sys.argv) >= 4 and sys.argv[2] == '--crop':
    crop_values = parse_crop_arg(sys.argv[3])

# Load image
image = cv2.imread(image_path)
if image is None:
    print("Image not found or unreadable:", image_path)
    sys.exit(1)

# Crop if crop values are given
if crop_values:
    h, w, _ = image.shape
    x1, y1, x2, y2 = crop_values
    image = image[int(h * y1):int(h * y2), int(w * x1):int(w * x2)]

# Run OCR
ocr = PaddleOCR(use_angle_cls=True, lang='ch')
result = ocr.ocr(image, cls=True)

# Print result
if result and result[0]:
    for line in result[0]:
        print(line[1][0])
else:
    print("No text found.")
