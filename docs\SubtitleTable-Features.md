# SubtitleTable - <PERSON><PERSON><PERSON> năng mới

## Tổng quan

SubtitleTable.vue đã đư<PERSON><PERSON> nâng cấp với các tính năng mới để quản lý subtitle hiệu quả hơn:

1. **Chèn subtitle** ở bất kỳ vị trí nào
2. **Tách subtitle** thành nhiều phần
3. **Hợp nhất subtitle** liền kề
4. **Sắp xếp lại ID** tự động
5. **Điều chỉnh thời gian** tự động

## Cấu trúc Components

### Components chính:
- `SubtitleTable.vue` - Component chính
- `SubtitleActions.vue` - Component con chứa các action buttons
- `SubtitleInsertModal.vue` - Modal để chèn subtitle mới
- `SubtitleSplitModal.vue` - Modal để tách subtitle

### Utility functions:
- `src/lib/subtitleUtils.js` - <PERSON><PERSON><PERSON> ti<PERSON> ích xử lý subtitle

## Cách sử dụng

### 1. Props mới của SubtitleTable

```vue
<SubtitleTable
  :subtitles="subtitles"
  :on-insert="handleInsert"
  :on-split="handleSplit"
  :on-merge="handleMerge"
  :on-reorder="handleReorder"
  :on-delete="handleDelete"
  <!-- ... các props khác -->
/>
```

### 2. Handler functions

#### handleInsert
```javascript
const handleInsert = (newSubtitle, targetId, position, adjustTiming = true) => {
  // newSubtitle: Object chứa thông tin subtitle mới
  // targetId: ID của subtitle làm mốc
  // position: 'before' hoặc 'after'
  // adjustTiming: có tự động điều chỉnh thời gian không
  
  const targetIndex = subtitles.value.findIndex(s => s.id === targetId);
  const insertIndex = position === 'before' ? targetIndex : targetIndex + 1;
  
  // Chèn subtitle mới
  subtitles.value.splice(insertIndex, 0, newSubtitle);
  
  // Sắp xếp lại ID
  subtitles.value = reorderSubtitleIds(subtitles.value);
  
  // Điều chỉnh thời gian nếu cần
  if (adjustTiming) {
    const insertDuration = newSubtitle.endTime - newSubtitle.startTime;
    subtitles.value = adjustSubtitleTimes(subtitles.value, insertIndex + 1, insertDuration);
  }
};
```

#### handleSplit
```javascript
const handleSplit = (id, splitSubtitles) => {
  // id: ID của subtitle cần tách
  // splitSubtitles: Array chứa 2 subtitle sau khi tách
  
  const index = subtitles.value.findIndex(s => s.id === id);
  
  // Thay thế subtitle gốc bằng 2 subtitle mới
  subtitles.value.splice(index, 1, ...splitSubtitles);
  
  // Sắp xếp lại ID
  subtitles.value = reorderSubtitleIds(subtitles.value);
};
```

#### handleMerge
```javascript
const handleMerge = (ids, mergedSubtitle) => {
  // ids: Array chứa ID của các subtitle cần hợp nhất
  // mergedSubtitle: Object subtitle sau khi hợp nhất
  
  // Tìm vị trí các subtitle
  const indices = ids.map(id => subtitles.value.findIndex(s => s.id === id))
                     .sort((a, b) => b - a);
  
  // Xóa các subtitle cũ (theo thứ tự ngược)
  indices.forEach(index => {
    subtitles.value.splice(index, 1);
  });
  
  // Chèn subtitle đã hợp nhất
  subtitles.value.splice(Math.min(...indices), 0, mergedSubtitle);
  
  // Sắp xếp lại ID
  subtitles.value = reorderSubtitleIds(subtitles.value);
};
```

### 3. Cấu trúc dữ liệu Subtitle

```javascript
const subtitle = {
  id: 1,                    // ID duy nhất
  index: 1,                 // Thứ tự trong danh sách
  start: '00:00:01,000',    // Thời gian bắt đầu (SRT format)
  end: '00:00:03,000',      // Thời gian kết thúc (SRT format)
  startTime: 1.0,           // Thời gian bắt đầu (seconds)
  endTime: 3.0,             // Thời gian kết thúc (seconds)
  text: 'Original text',    // Văn bản gốc
  translatedText: 'Dịch',   // Văn bản đã dịch
  status: 'translated',     // Trạng thái: pending, translating, translated, error
  isEnabled: true,          // Có được bật không
  // ... các thuộc tính khác cho audio generation
};
```

## Tính năng chi tiết

### 1. Chèn Subtitle

**Cách hoạt động:**
- Click vào dropdown button (icon +) trong cột Action
- Chọn "Chèn trước" hoặc "Chèn sau"
- Modal hiện ra cho phép nhập:
  - Thời gian bắt đầu/kết thúc
  - Văn bản gốc và dịch
  - Tùy chọn điều chỉnh thời gian tự động

**Tự động điều chỉnh:**
- Tính toán thời gian hợp lý dựa trên subtitle xung quanh
- Tự động sắp xếp lại ID
- Điều chỉnh thời gian các subtitle sau nếu cần

### 2. Tách Subtitle

**Cách hoạt động:**
- Click vào button tách (icon kéo) trong cột Action
- Modal hiện ra với:
  - Thanh trượt để chọn điểm tách
  - Input thời gian chính xác
  - Tự động chia văn bản theo tỷ lệ thời gian
  - Cho phép chỉnh sửa văn bản từng phần

**Tính năng thông minh:**
- Tự động tìm điểm tách hợp lý (dấu cách, dấu câu)
- Chia văn bản theo tỷ lệ thời gian
- Validation thời gian hợp lệ

### 3. Hợp nhất Subtitle

**Cách hoạt động:**
- Click vào button hợp nhất (icon merge) trong cột Action
- Tự động hợp nhất với subtitle tiếp theo
- Confirmation dialog trước khi thực hiện

**Xử lý dữ liệu:**
- Gộp văn bản với khoảng trắng
- Lấy thời gian từ subtitle đầu đến subtitle cuối
- Reset trạng thái audio generation

### 4. Sắp xếp lại ID

**Tự động thực hiện sau:**
- Chèn subtitle mới
- Xóa subtitle
- Tách subtitle
- Hợp nhất subtitle

**Đảm bảo:**
- ID liên tục từ 1, 2, 3...
- Index tương ứng với ID
- Thứ tự thời gian được duy trì

### 5. Điều chỉnh thời gian

**Khi chèn subtitle:**
- Tự động dịch chuyển thời gian các subtitle sau
- Tránh overlap giữa các subtitle
- Validation thời gian hợp lệ

## Utility Functions

### `subtitleUtils.js`

```javascript
// Chuyển đổi seconds sang SRT time format
secondsToSRTTime(seconds)

// Sắp xếp lại ID subtitle
reorderSubtitleIds(subtitles)

// Điều chỉnh thời gian subtitle
adjustSubtitleTimes(subtitles, insertIndex, insertDuration)

// Chèn subtitle mới
insertSubtitle(subtitles, targetId, position)

// Tách subtitle
splitSubtitle(subtitle)

// Hợp nhất subtitle
mergeSubtitles(firstSubtitle, secondSubtitle)

// Sửa subtitle overlap
fixOverlappingSubtitles(subtitles)

// Validation thời gian
validateSubtitleTiming(subtitle)
```

## Ví dụ sử dụng

Xem file `SubtitleTableDemo.vue` để có ví dụ đầy đủ về cách tích hợp và sử dụng các tính năng mới.

## Lưu ý

1. **Performance**: Với danh sách subtitle lớn, nên sử dụng pagination
2. **Validation**: Luôn validate dữ liệu trước khi xử lý
3. **Backup**: Nên có cơ chế undo/redo cho các thao tác quan trọng
4. **Audio sync**: Sau khi chỉnh sửa subtitle, cần regenerate audio tương ứng
