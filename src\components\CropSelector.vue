<template>
  <div 
    ref="cropContainer"
    class="crop-selector-container"
    :class="{ 'crop-active': isActive }"
    @mousedown="startCrop"
    @mousemove="onMouseMove"
    @mouseup="endCrop"
    @mouseleave="onMouseLeave"
  >
    <slot></slot>
    
    <!-- Crop overlay -->
    <div 
      v-if="isActive"
      class="crop-overlay"
      :style="overlayStyle"
    ></div>
    
    <!-- Selection rectangle -->
    <div 
      v-if="cropRect.width > 0 && cropRect.height > 0"
      class="crop-selection"
      :style="selectionStyle"
    >
      <!-- Corner handles -->
      <div class="crop-handle crop-handle-nw" @mousedown="startResize($event, 'nw')"></div>
      <div class="crop-handle crop-handle-ne" @mousedown="startResize($event, 'ne')"></div>
      <div class="crop-handle crop-handle-sw" @mousedown="startResize($event, 'sw')"></div>
      <div class="crop-handle crop-handle-se" @mousedown="startResize($event, 'se')"></div>
      
      <!-- Edge handles -->
      <div class="crop-handle crop-handle-n" @mousedown="startResize($event, 'n')"></div>
      <div class="crop-handle crop-handle-s" @mousedown="startResize($event, 's')"></div>
      <div class="crop-handle crop-handle-w" @mousedown="startResize($event, 'w')"></div>
      <div class="crop-handle crop-handle-e" @mousedown="startResize($event, 'e')"></div>
      
      <!-- Delete button -->
      <button 
        class="crop-delete-btn"
        @click="clearSelection"
        title="Clear selection"
      >
        ×
      </button>
    </div>
    
    <!-- Coordinates display -->
    <div 
      v-if="cropRect.width > 0 && cropRect.height > 0 && showCoordinates"
      class="crop-coordinates"
    >
      <div class="text-xs bg-black bg-opacity-75 text-white p-1 rounded">
        {{ Math.round(normalizedCrop.x) }}, {{ Math.round(normalizedCrop.y) }}, 
        {{ Math.round(normalizedCrop.width) }}, {{ Math.round(normalizedCrop.height) }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CropSelector',
  props: {
    // Whether crop selection is active
    active: {
      type: Boolean,
      default: false
    },
    // Original content dimensions (for accurate coordinate calculation)
    contentWidth: {
      type: Number,
      default: 0
    },
    contentHeight: {
      type: Number,
      default: 0
    },
    // Show coordinates display
    showCoordinates: {
      type: Boolean,
      default: true
    },
    // Initial crop area (normalized coordinates 0-1)
    initialCrop: {
      type: Object,
      default: () => ({ x: 0, y: 0, width: 0, height: 0 })
    }
  },
  data() {
    return {
      isActive: false,
      isDragging: false,
      isResizing: false,
      resizeHandle: null,
      cropRect: { x: 0, y: 0, width: 0, height: 0 },
      startPoint: { x: 0, y: 0 },
      containerRect: null,
      dragOffset: { x: 0, y: 0 }
    };
  },
  computed: {
    overlayStyle() {
      if (!this.containerRect) return {};
      
      return {
        position: 'absolute',
        top: '0',
        left: '0',
        right: '0',
        bottom: '0',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        pointerEvents: 'none',
        clipPath: this.cropRect.width > 0 && this.cropRect.height > 0 
          ? `polygon(0% 0%, 0% 100%, ${this.cropRect.x}px 100%, ${this.cropRect.x}px ${this.cropRect.y}px, ${this.cropRect.x + this.cropRect.width}px ${this.cropRect.y}px, ${this.cropRect.x + this.cropRect.width}px ${this.cropRect.y + this.cropRect.height}px, ${this.cropRect.x}px ${this.cropRect.y + this.cropRect.height}px, ${this.cropRect.x}px 100%, 100% 100%, 100% 0%)`
          : 'none'
      };
    },
    selectionStyle() {
      return {
        position: 'absolute',
        left: `${this.cropRect.x}px`,
        top: `${this.cropRect.y}px`,
        width: `${this.cropRect.width}px`,
        height: `${this.cropRect.height}px`,
      };
    },
    // Normalized coordinates for PaddleOCR (relative to original content dimensions)
    normalizedCrop() {
      if (!this.containerRect || !this.contentWidth || !this.contentHeight) {
        return { x: 0, y: 0, width: 0, height: 0 };
      }
      
      // Calculate scale factors
      const scaleX = this.contentWidth / this.containerRect.width;
      const scaleY = this.contentHeight / this.containerRect.height;
      
      return {
        x: this.cropRect.x * scaleX,
        y: this.cropRect.y * scaleY,
        width: this.cropRect.width * scaleX,
        height: this.cropRect.height * scaleY
      };
    }
  },
  watch: {
    active(newVal) {
      this.isActive = newVal;
      if (newVal) {
        this.updateContainerRect();
      }
    },
    initialCrop: {
      handler(newCrop) {
        if (newCrop && (newCrop.width > 0 || newCrop.height > 0)) {
          this.setCropFromNormalized(newCrop);
        }
      },
      immediate: true
    },
    normalizedCrop: {
      handler(newCrop) {
        this.$emit('crop-change', {
          normalized: newCrop,
          display: this.cropRect
        });
      },
      deep: true
    }
  },
  mounted() {
    document.addEventListener('mousemove', this.onGlobalMouseMove);
    document.addEventListener('mouseup', this.onGlobalMouseUp);
    window.addEventListener('resize', this.updateContainerRect);
  },
  beforeUnmount() {
    document.removeEventListener('mousemove', this.onGlobalMouseMove);
    document.removeEventListener('mouseup', this.onGlobalMouseUp);
    window.removeEventListener('resize', this.updateContainerRect);
  },
  methods: {
    updateContainerRect() {
      if (this.$refs.cropContainer) {
        this.containerRect = this.$refs.cropContainer.getBoundingClientRect();
      }
    },
    startCrop(event) {
      if (!this.isActive || this.isResizing) return;
      
      this.updateContainerRect();
      const rect = this.containerRect;
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      // Check if clicking inside existing selection for drag
      if (this.cropRect.width > 0 && this.cropRect.height > 0 &&
          x >= this.cropRect.x && x <= this.cropRect.x + this.cropRect.width &&
          y >= this.cropRect.y && y <= this.cropRect.y + this.cropRect.height) {
        // Start dragging existing selection
        this.isDragging = true;
        this.dragOffset = {
          x: x - this.cropRect.x,
          y: y - this.cropRect.y
        };
      } else {
        // Start new selection
        this.isDragging = true;
        this.startPoint = { x, y };
        this.cropRect = { x, y, width: 0, height: 0 };
      }
      
      event.preventDefault();
    },
    onMouseMove(event) {
      if (!this.isDragging || !this.containerRect) return;
      
      const rect = this.containerRect;
      const x = Math.max(0, Math.min(rect.width, event.clientX - rect.left));
      const y = Math.max(0, Math.min(rect.height, event.clientY - rect.top));
      
      if (this.dragOffset.x !== undefined) {
        // Dragging existing selection
        const newX = Math.max(0, Math.min(rect.width - this.cropRect.width, x - this.dragOffset.x));
        const newY = Math.max(0, Math.min(rect.height - this.cropRect.height, y - this.dragOffset.y));
        this.cropRect.x = newX;
        this.cropRect.y = newY;
      } else {
        // Creating new selection
        const width = Math.abs(x - this.startPoint.x);
        const height = Math.abs(y - this.startPoint.y);
        const left = Math.min(x, this.startPoint.x);
        const top = Math.min(y, this.startPoint.y);
        
        this.cropRect = { x: left, y: top, width, height };
      }
    },
    endCrop() {
      this.isDragging = false;
      this.dragOffset = {};
      
      // Emit final crop data
      if (this.cropRect.width > 5 && this.cropRect.height > 5) {
        this.$emit('crop-selected', {
          normalized: this.normalizedCrop,
          display: this.cropRect
        });
      }
    },
    onMouseLeave() {
      // Don't end crop on mouse leave, allow dragging outside
    },
    startResize(event, handle) {
      this.isResizing = true;
      this.resizeHandle = handle;
      this.updateContainerRect();
      event.stopPropagation();
      event.preventDefault();
    },
    onGlobalMouseMove(event) {
      if (this.isResizing && this.containerRect) {
        this.handleResize(event);
      } else if (this.isDragging && this.isActive) {
        this.onMouseMove(event);
      }
    },
    onGlobalMouseUp() {
      if (this.isResizing) {
        this.isResizing = false;
        this.resizeHandle = null;
      } else if (this.isDragging) {
        this.endCrop();
      }
    },
    handleResize(event) {
      const rect = this.containerRect;
      const x = Math.max(0, Math.min(rect.width, event.clientX - rect.left));
      const y = Math.max(0, Math.min(rect.height, event.clientY - rect.top));
      
      const { x: cropX, y: cropY, width: cropW, height: cropH } = this.cropRect;
      
      switch (this.resizeHandle) {
        case 'nw':
          this.cropRect = {
            x: Math.min(x, cropX + cropW - 10),
            y: Math.min(y, cropY + cropH - 10),
            width: Math.max(10, cropW + (cropX - x)),
            height: Math.max(10, cropH + (cropY - y))
          };
          break;
        case 'ne':
          this.cropRect = {
            x: cropX,
            y: Math.min(y, cropY + cropH - 10),
            width: Math.max(10, x - cropX),
            height: Math.max(10, cropH + (cropY - y))
          };
          break;
        case 'sw':
          this.cropRect = {
            x: Math.min(x, cropX + cropW - 10),
            y: cropY,
            width: Math.max(10, cropW + (cropX - x)),
            height: Math.max(10, y - cropY)
          };
          break;
        case 'se':
          this.cropRect = {
            x: cropX,
            y: cropY,
            width: Math.max(10, x - cropX),
            height: Math.max(10, y - cropY)
          };
          break;
        case 'n':
          this.cropRect = {
            x: cropX,
            y: Math.min(y, cropY + cropH - 10),
            width: cropW,
            height: Math.max(10, cropH + (cropY - y))
          };
          break;
        case 's':
          this.cropRect = {
            x: cropX,
            y: cropY,
            width: cropW,
            height: Math.max(10, y - cropY)
          };
          break;
        case 'w':
          this.cropRect = {
            x: Math.min(x, cropX + cropW - 10),
            y: cropY,
            width: Math.max(10, cropW + (cropX - x)),
            height: cropH
          };
          break;
        case 'e':
          this.cropRect = {
            x: cropX,
            y: cropY,
            width: Math.max(10, x - cropX),
            height: cropH
          };
          break;
      }
    },
    setCropFromNormalized(normalizedCrop) {
      if (!this.containerRect || !this.contentWidth || !this.contentHeight) {
        // Store for later when container is ready
        this.$nextTick(() => {
          this.updateContainerRect();
          this.setCropFromNormalized(normalizedCrop);
        });
        return;
      }
      
      const scaleX = this.containerRect.width / this.contentWidth;
      const scaleY = this.containerRect.height / this.contentHeight;
      
      this.cropRect = {
        x: normalizedCrop.x * scaleX,
        y: normalizedCrop.y * scaleY,
        width: normalizedCrop.width * scaleX,
        height: normalizedCrop.height * scaleY
      };
    },
    clearSelection() {
      this.cropRect = { x: 0, y: 0, width: 0, height: 0 };
      this.$emit('crop-cleared');
    },
    // Public methods
    activate() {
      this.isActive = true;
      this.updateContainerRect();
    },
    deactivate() {
      this.isActive = false;
    },
    getCropData() {
      return {
        normalized: this.normalizedCrop,
        display: this.cropRect
      };
    }
  }
};
</script>

<style scoped>
.crop-selector-container {
  position: relative;
  display: inline-block;
}

.crop-selector-container.crop-active {
  cursor: crosshair;
}

.crop-overlay {
  z-index: 10;
}

.crop-selection {
  border: 2px solid #3b82f6;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5);
  z-index: 15;
  cursor: move;
}

.crop-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border: 1px solid white;
  border-radius: 50%;
  z-index: 20;
}

.crop-handle-nw { top: -4px; left: -4px; cursor: nw-resize; }
.crop-handle-ne { top: -4px; right: -4px; cursor: ne-resize; }
.crop-handle-sw { bottom: -4px; left: -4px; cursor: sw-resize; }
.crop-handle-se { bottom: -4px; right: -4px; cursor: se-resize; }
.crop-handle-n { top: -4px; left: 50%; transform: translateX(-50%); cursor: n-resize; }
.crop-handle-s { bottom: -4px; left: 50%; transform: translateX(-50%); cursor: s-resize; }
.crop-handle-w { top: 50%; left: -4px; transform: translateY(-50%); cursor: w-resize; }
.crop-handle-e { top: 50%; right: -4px; transform: translateY(-50%); cursor: e-resize; }

.crop-delete-btn {
  position: absolute;
  top: -12px;
  right: -12px;
  width: 20px;
  height: 20px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 25;
}

.crop-delete-btn:hover {
  background: #dc2626;
}

.crop-coordinates {
  position: absolute;
  top: -30px;
  left: 0;
  z-index: 30;
  pointer-events: none;
}
</style>